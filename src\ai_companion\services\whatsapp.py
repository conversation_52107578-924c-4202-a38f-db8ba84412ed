"""
WhatsApp Service for the AI Companion System.
Handles WhatsApp bot integration for emotional support conversations.
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Callable
from urllib.parse import quote

import httpx
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import PlainTextResponse

from ..config.settings import settings
from ..core.models import InteractionType, EmotionType
from ..utils.helpers import generate_id, utc_now


class WhatsAppService:
    """WhatsApp Business API integration for AI companion bot."""
    
    def __init__(
        self,
        conversation_service=None,
        crisis_detection=None
    ):
        """Initialize WhatsApp service."""
        self.logger = logging.getLogger(__name__)
        self.conversation_service = conversation_service
        self.crisis_detection = crisis_detection
        
        # WhatsApp API configuration
        self.api_base_url = f"https://graph.facebook.com/v18.0/{settings.whatsapp_phone_number_id}"
        self.webhook_verify_token = settings.whatsapp_webhook_verify_token
        self.access_token = settings.whatsapp_access_token
        
        # HTTP client for API calls
        self.http_client = httpx.AsyncClient(
            timeout=30.0,
            headers={
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
        )
        
        # Message tracking
        self.active_conversations: Dict[str, Dict[str, Any]] = {}
        self.message_queue: List[Dict[str, Any]] = []
        
        # Rate limiting
        self.rate_limit_window = 60  # seconds
        self.max_messages_per_window = 20
        self.user_message_counts: Dict[str, List[float]] = {}
        
        self.logger.info("✅ WhatsApp service initialized")
    
    async def verify_webhook(self, mode: str, token: str, challenge: str) -> str:
        """Verify webhook for WhatsApp Business API."""
        if mode == "subscribe" and token == self.webhook_verify_token:
            self.logger.info("✅ WhatsApp webhook verified")
            return challenge
        else:
            self.logger.warning("❌ WhatsApp webhook verification failed")
            raise HTTPException(status_code=403, detail="Verification failed")
    
    async def handle_webhook(self, request_data: Dict[str, Any]) -> Dict[str, str]:
        """Handle incoming WhatsApp webhook messages."""
        try:
            # Extract message data
            entry = request_data.get("entry", [])
            if not entry:
                return {"status": "no_entry"}
            
            changes = entry[0].get("changes", [])
            if not changes:
                return {"status": "no_changes"}
            
            value = changes[0].get("value", {})
            messages = value.get("messages", [])
            
            # Process each message
            for message in messages:
                await self._process_incoming_message(message, value)
            
            return {"status": "success"}
            
        except Exception as e:
            self.logger.error(f"Error handling webhook: {e}")
            return {"status": "error", "message": str(e)}
    
    async def _process_incoming_message(self, message: Dict[str, Any], value: Dict[str, Any]):
        """Process a single incoming WhatsApp message."""
        try:
            # Extract message details
            from_number = message.get("from")
            message_id = message.get("id")
            timestamp = message.get("timestamp")
            message_type = message.get("type")
            
            if not from_number or not message_id:
                self.logger.warning("Invalid message format")
                return
            
            # Check rate limiting
            if not self._check_rate_limit(from_number):
                await self.send_message(
                    from_number,
                    "I notice you're sending messages very quickly. Let's take a moment to breathe. I'm here to listen when you're ready. 🌸"
                )
                return
            
            # Extract message content based on type
            content = ""
            if message_type == "text":
                content = message.get("text", {}).get("body", "")
            elif message_type == "audio":
                # Handle audio messages (could implement speech-to-text)
                content = "[Audio message received - audio processing not yet implemented]"
            elif message_type == "image":
                content = "[Image received]"
                caption = message.get("image", {}).get("caption", "")
                if caption:
                    content += f" - {caption}"
            else:
                content = f"[{message_type} message received]"
            
            if not content.strip():
                return
            
            # Mark message as read
            await self._mark_message_read(message_id)
            
            # Show typing indicator
            await self._send_typing_indicator(from_number)
            
            # Process conversation
            await self._handle_conversation(from_number, content, message_id)
            
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
    
    async def _handle_conversation(self, phone_number: str, message: str, message_id: str):
        """Handle conversation logic for WhatsApp message."""
        try:
            # Use phone number as user ID (could be enhanced with user registration)
            user_id = f"whatsapp_{phone_number}"
            
            # Track conversation state
            if user_id not in self.active_conversations:
                self.active_conversations[user_id] = {
                    "start_time": utc_now(),
                    "message_count": 0,
                    "last_activity": utc_now()
                }
            
            conversation_state = self.active_conversations[user_id]
            conversation_state["message_count"] += 1
            conversation_state["last_activity"] = utc_now()
            
            # Check for crisis indicators
            crisis_result = await self.crisis_detection.analyze_message(message, user_id)
            
            if crisis_result.get("is_crisis", False):
                await self._handle_crisis_situation(phone_number, crisis_result)
                return
            
            # Generate AI response
            response = await self.conversation_service.process_message(
                user_id=user_id,
                message=message,
                interaction_type=InteractionType.CONVERSATION,
                context={
                    "platform": "whatsapp",
                    "phone_number": phone_number,
                    "conversation_state": conversation_state
                }
            )
            
            if response and response.get("response"):
                # Send response via WhatsApp
                await self.send_message(phone_number, response["response"])
                
                # Handle any follow-up actions
                if response.get("suggested_actions"):
                    await self._handle_suggested_actions(phone_number, response["suggested_actions"])
            
        except Exception as e:
            self.logger.error(f"Error handling conversation: {e}")
            # Send fallback message
            await self.send_message(
                phone_number,
                "I'm experiencing some technical difficulties right now. Please try again in a moment. If this is urgent, please reach out to a mental health professional. 💙"
            )
    
    async def _handle_crisis_situation(self, phone_number: str, crisis_result: Dict[str, Any]):
        """Handle crisis situations with immediate intervention."""
        try:
            risk_level = crisis_result.get("risk_level", "moderate")
            
            if risk_level == "critical":
                crisis_message = """🚨 I'm very concerned about what you've shared. Your safety is the most important thing right now.

Please reach out for immediate help:
• National Suicide Prevention Lifeline: 988
• Crisis Text Line: Text HOME to 741741
• Emergency Services: 911

You don't have to go through this alone. There are people who want to help you. 💙"""
            
            elif risk_level == "high":
                crisis_message = """I can hear that you're going through something really difficult right now. Your feelings are valid, and I'm glad you're reaching out.

If you're having thoughts of hurting yourself, please consider:
• National Suicide Prevention Lifeline: 988
• Crisis Text Line: Text HOME to 741741
• Talking to a trusted friend, family member, or counselor

Would you like to talk about what's going on? I'm here to listen. 🌟"""
            
            else:  # moderate
                crisis_message = """I notice you might be struggling right now. It takes courage to share difficult feelings, and I want you to know that you're not alone.

If you need additional support:
• National Suicide Prevention Lifeline: 988
• Crisis Text Line: Text HOME to 741741

I'm here to listen and support you. What would be most helpful right now? 💙"""
            
            await self.send_message(phone_number, crisis_message)
            
            # Log crisis intervention
            self.logger.warning(f"Crisis intervention triggered for {phone_number}: {risk_level}")
            
        except Exception as e:
            self.logger.error(f"Error handling crisis situation: {e}")
    
    async def send_message(self, phone_number: str, message: str) -> bool:
        """Send a message via WhatsApp Business API."""
        try:
            # Clean phone number (remove any formatting)
            clean_number = phone_number.replace("+", "").replace("-", "").replace(" ", "")
            
            payload = {
                "messaging_product": "whatsapp",
                "to": clean_number,
                "type": "text",
                "text": {
                    "body": message
                }
            }
            
            response = await self.http_client.post(
                f"{self.api_base_url}/messages",
                json=payload
            )
            
            if response.status_code == 200:
                self.logger.info(f"Message sent successfully to {phone_number}")
                return True
            else:
                self.logger.error(f"Failed to send message: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
            return False
    
    async def send_template_message(
        self,
        phone_number: str,
        template_name: str,
        language_code: str = "en_US",
        parameters: List[str] = None
    ) -> bool:
        """Send a template message (for notifications, etc.)."""
        try:
            clean_number = phone_number.replace("+", "").replace("-", "").replace(" ", "")
            
            payload = {
                "messaging_product": "whatsapp",
                "to": clean_number,
                "type": "template",
                "template": {
                    "name": template_name,
                    "language": {
                        "code": language_code
                    }
                }
            }
            
            if parameters:
                payload["template"]["components"] = [{
                    "type": "body",
                    "parameters": [{"type": "text", "text": param} for param in parameters]
                }]
            
            response = await self.http_client.post(
                f"{self.api_base_url}/messages",
                json=payload
            )
            
            return response.status_code == 200
            
        except Exception as e:
            self.logger.error(f"Error sending template message: {e}")
            return False
    
    async def _mark_message_read(self, message_id: str):
        """Mark a message as read."""
        try:
            payload = {
                "messaging_product": "whatsapp",
                "status": "read",
                "message_id": message_id
            }
            
            await self.http_client.post(
                f"{self.api_base_url}/messages",
                json=payload
            )
            
        except Exception as e:
            self.logger.error(f"Error marking message as read: {e}")
    
    async def _send_typing_indicator(self, phone_number: str):
        """Send typing indicator to show the bot is processing."""
        try:
            # Simulate typing delay
            await asyncio.sleep(1)
            
        except Exception as e:
            self.logger.error(f"Error sending typing indicator: {e}")
    
    def _check_rate_limit(self, phone_number: str) -> bool:
        """Check if user is within rate limits."""
        current_time = time.time()
        
        if phone_number not in self.user_message_counts:
            self.user_message_counts[phone_number] = []
        
        # Clean old timestamps
        self.user_message_counts[phone_number] = [
            timestamp for timestamp in self.user_message_counts[phone_number]
            if current_time - timestamp < self.rate_limit_window
        ]
        
        # Check if under limit
        if len(self.user_message_counts[phone_number]) >= self.max_messages_per_window:
            return False
        
        # Add current timestamp
        self.user_message_counts[phone_number].append(current_time)
        return True
    
    async def _handle_suggested_actions(self, phone_number: str, actions: List[Dict[str, Any]]):
        """Handle suggested follow-up actions."""
        try:
            for action in actions:
                action_type = action.get("type")
                
                if action_type == "check_in":
                    # Schedule a check-in message
                    delay = action.get("delay_minutes", 60)
                    asyncio.create_task(self._schedule_check_in(phone_number, delay))
                
                elif action_type == "resource_sharing":
                    # Send helpful resources
                    resources = action.get("resources", [])
                    if resources:
                        resource_message = "Here are some resources that might be helpful:\n\n"
                        resource_message += "\n".join(f"• {resource}" for resource in resources)
                        await self.send_message(phone_number, resource_message)
                
        except Exception as e:
            self.logger.error(f"Error handling suggested actions: {e}")
    
    async def _schedule_check_in(self, phone_number: str, delay_minutes: int):
        """Schedule a check-in message."""
        try:
            await asyncio.sleep(delay_minutes * 60)
            
            check_in_message = "Hi! I wanted to check in and see how you're doing. How are you feeling right now? 🌟"
            await self.send_message(phone_number, check_in_message)
            
        except Exception as e:
            self.logger.error(f"Error sending check-in message: {e}")
    
    async def broadcast_message(self, phone_numbers: List[str], message: str) -> Dict[str, bool]:
        """Broadcast a message to multiple users."""
        results = {}
        
        for phone_number in phone_numbers:
            try:
                success = await self.send_message(phone_number, message)
                results[phone_number] = success
                
                # Add delay to avoid rate limiting
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error broadcasting to {phone_number}: {e}")
                results[phone_number] = False
        
        return results
    
    async def get_conversation_stats(self) -> Dict[str, Any]:
        """Get WhatsApp conversation statistics."""
        try:
            total_conversations = len(self.active_conversations)
            active_conversations = sum(
                1 for conv in self.active_conversations.values()
                if (utc_now() - conv["last_activity"]).total_seconds() < 3600  # Active in last hour
            )
            
            total_messages = sum(
                conv["message_count"] for conv in self.active_conversations.values()
            )
            
            return {
                "total_conversations": total_conversations,
                "active_conversations": active_conversations,
                "total_messages": total_messages,
                "platform": "whatsapp"
            }
            
        except Exception as e:
            self.logger.error(f"Error getting conversation stats: {e}")
            return {}
    
    async def close(self):
        """Close WhatsApp service and cleanup resources."""
        try:
            await self.http_client.aclose()
            self.logger.info("WhatsApp service closed")
            
        except Exception as e:
            self.logger.error(f"Error closing WhatsApp service: {e}")


def create_whatsapp_webhook_app(whatsapp_service) -> FastAPI:
    """Create FastAPI app for WhatsApp webhook."""
    app = FastAPI(title="WhatsApp Webhook", version="1.0.0")
    
    @app.get("/webhook")
    async def verify_webhook(
        hub_mode: str = None,
        hub_verify_token: str = None,
        hub_challenge: str = None
    ):
        """Verify WhatsApp webhook."""
        challenge = await whatsapp_service.verify_webhook(
            hub_mode, hub_verify_token, hub_challenge
        )
        return PlainTextResponse(challenge)
    
    @app.post("/webhook")
    async def handle_webhook(request: Request):
        """Handle WhatsApp webhook messages."""
        request_data = await request.json()
        result = await whatsapp_service.handle_webhook(request_data)
        return result
    
    return app
