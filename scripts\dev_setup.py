#!/usr/bin/env python3
"""
Development Environment Setup Script for AI Companion System
Automates the setup of development environment with all necessary dependencies and configurations.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command, description, check=True):
    """Run a shell command with error handling."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed: {result.stderr}")
            return False
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    print("🔍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 9:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible. Need Python 3.9+")
        return False


def check_git():
    """Check if git is available."""
    print("🔍 Checking Git availability...")
    if shutil.which("git"):
        print("✅ Git is available")
        return True
    else:
        print("❌ Git is not available. Please install Git.")
        return False


def create_directories():
    """Create necessary directories."""
    print("📁 Creating necessary directories...")
    directories = [
        "data/db",
        "data/logs", 
        "data/cache",
        "backups",
        "tests/fixtures",
        "docs/images"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        # Create .gitkeep files to preserve empty directories
        gitkeep_file = Path(directory) / ".gitkeep"
        if not gitkeep_file.exists():
            gitkeep_file.touch()
    
    print("✅ Directories created successfully")


def setup_virtual_environment():
    """Set up Python virtual environment."""
    print("🐍 Setting up virtual environment...")
    
    if Path("venv").exists():
        print("⚠️  Virtual environment already exists. Skipping creation.")
        return True
    
    # Create virtual environment
    if not run_command("python -m venv venv", "Creating virtual environment"):
        return False
    
    # Activate and upgrade pip
    if os.name == 'nt':  # Windows
        activate_cmd = "venv\\Scripts\\activate"
        pip_cmd = "venv\\Scripts\\pip"
    else:  # Unix/Linux/macOS
        activate_cmd = "source venv/bin/activate"
        pip_cmd = "venv/bin/pip"
    
    if not run_command(f"{pip_cmd} install --upgrade pip", "Upgrading pip"):
        return False
    
    print("✅ Virtual environment setup completed")
    return True


def install_dependencies():
    """Install project dependencies."""
    print("📦 Installing dependencies...")
    
    # Determine pip command based on OS
    if os.name == 'nt':  # Windows
        pip_cmd = "venv\\Scripts\\pip"
    else:  # Unix/Linux/macOS
        pip_cmd = "venv/bin/pip"
    
    # Install production dependencies
    if not run_command(f"{pip_cmd} install -r requirements.txt", "Installing production dependencies"):
        return False
    
    # Install development dependencies
    if Path("requirements-dev.txt").exists():
        if not run_command(f"{pip_cmd} install -r requirements-dev.txt", "Installing development dependencies"):
            return False
    
    print("✅ Dependencies installed successfully")
    return True


def setup_pre_commit_hooks():
    """Set up pre-commit hooks for code quality."""
    print("🔧 Setting up pre-commit hooks...")
    
    # Check if pre-commit is available
    if os.name == 'nt':  # Windows
        precommit_cmd = "venv\\Scripts\\pre-commit"
    else:  # Unix/Linux/macOS
        precommit_cmd = "venv/bin/pre-commit"
    
    # Install pre-commit hooks if pre-commit is available
    if Path(precommit_cmd).exists() or shutil.which("pre-commit"):
        run_command(f"{precommit_cmd} install", "Installing pre-commit hooks", check=False)
    else:
        print("⚠️  pre-commit not available. Skipping hook setup.")
    
    return True


def validate_setup():
    """Validate the development setup."""
    print("🧪 Validating setup...")
    
    # Check if main module can be imported
    if os.name == 'nt':  # Windows
        python_cmd = "venv\\Scripts\\python"
    else:  # Unix/Linux/macOS
        python_cmd = "venv/bin/python"
    
    # Test import
    test_import = f"{python_cmd} -c \"import sys; sys.path.insert(0, 'src'); from ai_companion.config.settings import settings; print('✅ Import test passed')\""
    
    if run_command(test_import, "Testing module imports", check=False):
        print("✅ Setup validation passed")
        return True
    else:
        print("⚠️  Setup validation had issues, but this might be due to missing API keys")
        return True


def main():
    """Main setup function."""
    print("🚀 AI Companion System - Development Environment Setup")
    print("=" * 60)
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    if not check_git():
        print("⚠️  Git not available. Some features may not work.")
    
    # Setup steps
    steps = [
        ("Creating directories", create_directories),
        ("Setting up virtual environment", setup_virtual_environment),
        ("Installing dependencies", install_dependencies),
        ("Setting up pre-commit hooks", setup_pre_commit_hooks),
        ("Validating setup", validate_setup),
    ]
    
    failed_steps = []
    
    for step_name, step_function in steps:
        print(f"\n📋 {step_name}...")
        if not step_function():
            failed_steps.append(step_name)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SETUP SUMMARY")
    print("=" * 60)
    
    if not failed_steps:
        print("🎉 Development environment setup completed successfully!")
        print("\n🚀 Next steps:")
        print("1. Activate virtual environment:")
        if os.name == 'nt':  # Windows
            print("   venv\\Scripts\\activate")
        else:  # Unix/Linux/macOS
            print("   source venv/bin/activate")
        print("2. Configure your .env file with API keys")
        print("3. Run system tests: python test_system.py")
        print("4. Start development: make run")
        
    else:
        print(f"⚠️  Setup completed with {len(failed_steps)} issues:")
        for step in failed_steps:
            print(f"   - {step}")
        print("\nPlease resolve these issues before proceeding.")
    
    return len(failed_steps) == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
