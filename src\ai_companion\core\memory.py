"""
Memory Service for the AI Companion System.
Implements dual-memory architecture with personal and universal memory stores.
"""

import asyncio
import logging
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Set
from collections import defaultdict

from .models import (
    MemoryType, InteractionType, EmotionType,
    MemoryEntry, PersonalMemory, UniversalMemory,
    calculate_memory_importance, utc_now, generate_id
)
from ..services.storage import StorageService
from ..utils.helpers import extract_keywords, calculate_similarity
from ..config.settings import settings


class MemoryService:
    """Advanced memory service with dual-memory architecture."""
    
    def __init__(self, storage_service: StorageService):
        """Initialize the memory service."""
        self.storage_service = storage_service
        
        # Memory configuration
        self.personal_memory_limit = settings.personal_memory_size
        self.universal_memory_limit = settings.universal_memory_size
        self.memory_ttl = settings.memory_ttl
        
        # In-memory caches for performance
        self.personal_memory_cache: Dict[str, List[PersonalMemory]] = defaultdict(list)
        self.universal_memory_cache: List[UniversalMemory] = []
        self.cache_last_updated: Dict[str, datetime] = {}
        
        # Memory statistics
        self.memory_stats = {
            "personal_memories_stored": 0,
            "universal_memories_stored": 0,
            "memories_retrieved": 0,
            "cache_hits": 0,
            "cache_misses": 0
        }
        
        self.logger = logging.getLogger(__name__)
    
    async def store_memory(
        self,
        user_id: str,
        content: str,
        interaction_type: InteractionType,
        emotion: Optional[EmotionType] = None,
        importance: Optional[float] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> MemoryEntry:
        """Store a new memory entry."""
        try:
            # Calculate importance if not provided
            if importance is None:
                importance = calculate_memory_importance(
                    emotion=emotion,
                    interaction_type=interaction_type,
                    user_frequency=len(self.personal_memory_cache.get(user_id, [])),
                    content_length=len(content)
                )
            
            # Create memory entry
            memory = PersonalMemory(
                user_id=user_id,
                interaction_type=interaction_type,
                content=content,
                emotion=emotion,
                importance=importance,
                context=context or {}
            )
            
            # Store in database
            await self.storage_service.store_personal_memory(memory)
            
            # Update cache
            self._update_personal_cache(user_id, memory)
            
            # Update statistics
            self.memory_stats["personal_memories_stored"] += 1
            
            self.logger.debug(f"Stored personal memory for user {user_id}")
            return memory
            
        except Exception as e:
            self.logger.error(f"Error storing memory for user {user_id}: {e}")
            raise
    
    async def retrieve_memories(
        self,
        user_id: str,
        query: str,
        limit: int = 10,
        memory_types: Optional[List[MemoryType]] = None,
        min_importance: float = 0.0
    ) -> List[MemoryEntry]:
        """Retrieve relevant memories for a user query."""
        try:
            start_time = time.time()
            
            if memory_types is None:
                memory_types = [MemoryType.PERSONAL, MemoryType.UNIVERSAL]
            
            all_memories = []
            
            # Retrieve personal memories
            if MemoryType.PERSONAL in memory_types:
                personal_memories = await self._retrieve_personal_memories(
                    user_id, query, limit, min_importance
                )
                all_memories.extend(personal_memories)
            
            # Retrieve universal memories
            if MemoryType.UNIVERSAL in memory_types:
                universal_memories = await self._retrieve_universal_memories(
                    query, limit, min_importance
                )
                all_memories.extend(universal_memories)
            
            # Sort by relevance and importance
            scored_memories = []
            query_keywords = extract_keywords(query.lower())
            
            for memory in all_memories:
                # Calculate relevance score
                relevance_score = self._calculate_relevance_score(memory, query_keywords)
                
                # Combine relevance and importance
                final_score = (relevance_score * 0.7) + (memory.importance * 0.3)
                
                scored_memories.append((memory, final_score))
            
            # Sort by score and return top results
            scored_memories.sort(key=lambda x: x[1], reverse=True)
            result_memories = [memory for memory, score in scored_memories[:limit]]
            
            # Update access statistics
            for memory in result_memories:
                memory.last_accessed = utc_now()
                memory.access_count += 1
            
            # Update statistics
            self.memory_stats["memories_retrieved"] += len(result_memories)
            
            retrieval_time = time.time() - start_time
            self.logger.debug(f"Retrieved {len(result_memories)} memories in {retrieval_time:.3f}s")
            
            return result_memories
            
        except Exception as e:
            self.logger.error(f"Error retrieving memories for user {user_id}: {e}")
            return []
    
    async def _retrieve_personal_memories(
        self,
        user_id: str,
        query: str,
        limit: int,
        min_importance: float
    ) -> List[PersonalMemory]:
        """Retrieve personal memories for a user."""
        # Check cache first
        if self._is_cache_valid(user_id):
            cached_memories = self.personal_memory_cache.get(user_id, [])
            self.memory_stats["cache_hits"] += 1
        else:
            # Load from database
            cached_memories = await self.storage_service.get_personal_memories(user_id)
            self.personal_memory_cache[user_id] = cached_memories
            self.cache_last_updated[user_id] = utc_now()
            self.memory_stats["cache_misses"] += 1
        
        # Filter by importance
        relevant_memories = [
            memory for memory in cached_memories
            if memory.importance >= min_importance
        ]
        
        return relevant_memories[:limit]
    
    async def _retrieve_universal_memories(
        self,
        query: str,
        limit: int,
        min_importance: float
    ) -> List[UniversalMemory]:
        """Retrieve universal memories."""
        # Check cache
        if not self.universal_memory_cache or self._should_refresh_universal_cache():
            self.universal_memory_cache = await self.storage_service.get_universal_memories()
            self.memory_stats["cache_misses"] += 1
        else:
            self.memory_stats["cache_hits"] += 1
        
        # Filter by importance and relevance
        query_keywords = extract_keywords(query.lower())
        relevant_memories = []
        
        for memory in self.universal_memory_cache:
            if memory.importance >= min_importance:
                # Check topic relevance
                topic_match = any(
                    keyword in tag.lower() for keyword in query_keywords
                    for tag in memory.topic_tags
                )
                
                if topic_match or self._calculate_relevance_score(memory, query_keywords) > 0.3:
                    relevant_memories.append(memory)
        
        return relevant_memories[:limit]
    
    def _calculate_relevance_score(self, memory: MemoryEntry, query_keywords: List[str]) -> float:
        """Calculate relevance score between memory and query."""
        memory_keywords = memory.get_search_keywords()
        
        if not memory_keywords or not query_keywords:
            return 0.0
        
        # Calculate keyword overlap
        memory_set = set(memory_keywords)
        query_set = set(query_keywords)
        
        intersection = memory_set.intersection(query_set)
        union = memory_set.union(query_set)
        
        if not union:
            return 0.0
        
        # Jaccard similarity
        jaccard_score = len(intersection) / len(union)
        
        # Boost score for exact content matches
        content_similarity = calculate_similarity(memory.content.lower(), " ".join(query_keywords))
        
        # Combine scores
        final_score = (jaccard_score * 0.6) + (content_similarity * 0.4)
        
        return min(final_score, 1.0)
    
    def _update_personal_cache(self, user_id: str, memory: PersonalMemory):
        """Update personal memory cache."""
        user_memories = self.personal_memory_cache[user_id]
        user_memories.append(memory)
        
        # Keep cache size manageable
        if len(user_memories) > self.personal_memory_limit:
            # Remove oldest, least important memories
            user_memories.sort(key=lambda m: (m.importance, m.created_at), reverse=True)
            self.personal_memory_cache[user_id] = user_memories[:self.personal_memory_limit]
        
        self.cache_last_updated[user_id] = utc_now()
    
    def _update_universal_cache(self, memory: UniversalMemory):
        """Update universal memory cache."""
        self.universal_memory_cache.append(memory)
        
        # Keep cache size manageable
        if len(self.universal_memory_cache) > self.universal_memory_limit:
            # Remove oldest, least important memories
            self.universal_memory_cache.sort(key=lambda m: (m.importance, m.created_at), reverse=True)
            self.universal_memory_cache = self.universal_memory_cache[:self.universal_memory_limit]
    
    def _is_cache_valid(self, user_id: str) -> bool:
        """Check if user's memory cache is still valid."""
        if user_id not in self.cache_last_updated:
            return False
        
        cache_age = utc_now() - self.cache_last_updated[user_id]
        return cache_age.total_seconds() < 300  # 5 minutes
    
    def _should_refresh_universal_cache(self) -> bool:
        """Check if universal memory cache should be refreshed."""
        if not hasattr(self, '_universal_cache_last_updated'):
            return True
        
        cache_age = utc_now() - self._universal_cache_last_updated
        return cache_age.total_seconds() > 600  # 10 minutes
    
    async def cleanup_old_memories(self):
        """Clean up old, unused memories."""
        try:
            cutoff_date = utc_now() - timedelta(seconds=self.memory_ttl)
            
            # Clean up personal memories
            for user_id in list(self.personal_memory_cache.keys()):
                user_memories = self.personal_memory_cache[user_id]
                active_memories = [
                    memory for memory in user_memories
                    if memory.last_accessed > cutoff_date or memory.importance > 0.8
                ]
                
                if len(active_memories) != len(user_memories):
                    self.personal_memory_cache[user_id] = active_memories
                    self.logger.debug(f"Cleaned up {len(user_memories) - len(active_memories)} memories for user {user_id}")
            
            # Clean up universal memories
            active_universal = [
                memory for memory in self.universal_memory_cache
                if memory.last_accessed > cutoff_date or memory.importance > 0.8
            ]
            
            if len(active_universal) != len(self.universal_memory_cache):
                removed_count = len(self.universal_memory_cache) - len(active_universal)
                self.universal_memory_cache = active_universal
                self.logger.debug(f"Cleaned up {removed_count} universal memories")
            
        except Exception as e:
            self.logger.error(f"Error during memory cleanup: {e}")
    
    def get_memory_statistics(self) -> Dict[str, Any]:
        """Get memory service statistics."""
        total_personal = sum(len(memories) for memories in self.personal_memory_cache.values())
        total_universal = len(self.universal_memory_cache)
        
        cache_hit_rate = 0.0
        total_requests = self.memory_stats["cache_hits"] + self.memory_stats["cache_misses"]
        if total_requests > 0:
            cache_hit_rate = self.memory_stats["cache_hits"] / total_requests
        
        return {
            **self.memory_stats,
            "total_personal_memories": total_personal,
            "total_universal_memories": total_universal,
            "active_users": len(self.personal_memory_cache),
            "cache_hit_rate": cache_hit_rate
        }

    async def get_personal_memories(self, user_id: str, limit: int = 50) -> List[PersonalMemory]:
        """Get personal memories for a user."""
        try:
            return await self.storage_service.get_personal_memories(user_id, limit)
        except Exception as e:
            self.logger.error(f"Error getting personal memories: {e}")
            return []
