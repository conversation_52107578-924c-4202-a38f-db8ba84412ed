"""
Core Mental Health Analytics Service.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import defaultdict

from ...core.models import utc_now
from ...services.storage import StorageService
from ..privacy import DataAnonymizer
from .models import AnalyticsInsight, UserAnalytics, PopulationAnalytics
from .individual import IndividualAnalyzer
from .population import PopulationAnalyzer
from ...config.settings import settings


class MentalHealthAnalytics:
    """Main mental health analytics service with privacy-first design."""
    
    def __init__(self, storage_service: StorageService):
        """Initialize mental health analytics service."""
        self.logger = logging.getLogger(__name__)
        self.storage_service = storage_service
        self.anonymizer = DataAnonymizer()
        
        # Initialize analyzers
        self.individual_analyzer = IndividualAnalyzer(storage_service)
        self.population_analyzer = PopulationAnalyzer(storage_service)
        
        # Analytics cache
        self.insights_cache: Dict[str, List[AnalyticsInsight]] = defaultdict(list)
        self.cache_ttl = 3600  # 1 hour cache
        
        # Minimum cohort sizes for privacy
        self.min_cohort_size = settings.min_cohort_size
        
        self.logger.info("✅ Mental Health Analytics Service initialized")
    
    async def generate_insights(
        self,
        user_id: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate mental health insights with privacy protection."""
        try:
            # Set default date range
            if not end_date:
                end_date = utc_now()
            if not start_date:
                start_date = end_date - timedelta(days=30)
            
            # Determine analysis type
            if user_id:
                # Individual user analysis
                user_analytics = await self.individual_analyzer.analyze_user(
                    user_id, start_date, end_date, filters
                )
                return self._format_individual_insights(user_analytics)
            else:
                # Population-level analysis (anonymized)
                population_analytics = await self.population_analyzer.analyze_population(
                    start_date, end_date, filters
                )
                return self._format_population_insights(population_analytics)
                
        except Exception as e:
            self.logger.error(f"Error generating insights: {e}")
            return {"error": str(e), "timestamp": utc_now()}
    
    def _format_individual_insights(self, analytics: UserAnalytics) -> Dict[str, Any]:
        """Format individual analytics for API response."""
        return {
            "type": "individual",
            "user_id": analytics.user_id,
            "emotional_patterns": analytics.emotional_patterns,
            "interaction_patterns": analytics.interaction_patterns,
            "progress_trends": analytics.progress_trends,
            "scores": {
                "engagement": analytics.engagement_score,
                "consistency": analytics.consistency_score
            },
            "recommendations": analytics.recommendations,
            "timestamp": analytics.timestamp,
            "privacy_note": "This analysis is based on your personal data only"
        }
    
    def _format_population_insights(self, analytics: PopulationAnalytics) -> Dict[str, Any]:
        """Format population analytics for API response."""
        return {
            "type": "population",
            "total_users": analytics.total_users,
            "emotion_trends": analytics.emotion_trends,
            "risk_patterns": analytics.risk_patterns,
            "intervention_effectiveness": analytics.intervention_effectiveness,
            "recommendations": analytics.recommendations,
            "timestamp": analytics.timestamp,
            "privacy_note": "This analysis uses anonymized, aggregated data only"
        }
    
    async def get_platform_summary(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Get high-level platform summary with privacy protection."""
        try:
            # Get basic aggregated statistics
            total_users = await self.storage_service.count_active_users(start_date, end_date)
            total_interactions = await self.storage_service.count_interactions(start_date, end_date)
            
            # Only provide detailed analysis if we have sufficient users for privacy
            if total_users >= self.min_cohort_size:
                population_analytics = await self.population_analyzer.analyze_population(
                    start_date, end_date
                )
                
                return {
                    "platform_health": "detailed_analysis_available",
                    "total_users": total_users,
                    "total_interactions": total_interactions,
                    "emotion_trends": population_analytics.emotion_trends,
                    "risk_overview": population_analytics.risk_patterns,
                    "recommendations": population_analytics.recommendations,
                    "timestamp": utc_now()
                }
            else:
                return {
                    "platform_health": "limited_analysis_privacy_protection",
                    "total_users": "< " + str(self.min_cohort_size),
                    "total_interactions": total_interactions,
                    "note": f"Detailed analysis requires at least {self.min_cohort_size} users for privacy protection",
                    "timestamp": utc_now()
                }
                
        except Exception as e:
            self.logger.error(f"Error getting platform summary: {e}")
            return {"error": str(e), "timestamp": utc_now()}
    
    async def generate_insight(
        self,
        insight_type: str,
        data: Dict[str, Any],
        confidence: float = 0.8
    ) -> AnalyticsInsight:
        """Generate a specific analytics insight."""
        try:
            insight_id = f"insight_{insight_type}_{int(utc_now().timestamp())}"
            
            # Generate insight based on type
            if insight_type == "emotional_pattern":
                title, description = self._generate_emotional_insight(data)
            elif insight_type == "risk_assessment":
                title, description = self._generate_risk_insight(data)
            elif insight_type == "engagement_trend":
                title, description = self._generate_engagement_insight(data)
            else:
                title = f"General Insight: {insight_type}"
                description = "Analysis completed successfully"
            
            insight = AnalyticsInsight(
                insight_id=insight_id,
                title=title,
                description=description,
                data=data,
                confidence=confidence,
                timestamp=utc_now(),
                category=insight_type
            )
            
            # Cache the insight
            self.insights_cache[insight_type].append(insight)
            
            return insight
            
        except Exception as e:
            self.logger.error(f"Error generating insight: {e}")
            raise
    
    def _generate_emotional_insight(self, data: Dict[str, Any]) -> tuple[str, str]:
        """Generate emotional pattern insight."""
        dominant_emotions = data.get("dominant_emotions", [])
        if dominant_emotions:
            title = f"Dominant Emotional Pattern: {', '.join(dominant_emotions[:2])}"
            description = f"Analysis shows primary emotional patterns of {', '.join(dominant_emotions)}"
        else:
            title = "Emotional Pattern Analysis"
            description = "Emotional patterns analyzed successfully"
        
        return title, description
    
    def _generate_risk_insight(self, data: Dict[str, Any]) -> tuple[str, str]:
        """Generate risk assessment insight."""
        risk_level = data.get("risk_level", "unknown")
        title = f"Risk Assessment: {risk_level.title()} Level"
        description = f"Current mental health risk assessment indicates {risk_level} level"
        
        return title, description
    
    def _generate_engagement_insight(self, data: Dict[str, Any]) -> tuple[str, str]:
        """Generate engagement trend insight."""
        engagement_score = data.get("engagement_score", 0)
        if engagement_score > 0.7:
            title = "High Engagement Detected"
            description = "User shows strong engagement with the platform"
        elif engagement_score > 0.4:
            title = "Moderate Engagement"
            description = "User shows moderate engagement levels"
        else:
            title = "Low Engagement"
            description = "User engagement could be improved"
        
        return title, description
    
    async def get_cached_insights(
        self,
        insight_type: Optional[str] = None,
        limit: int = 10
    ) -> List[AnalyticsInsight]:
        """Get cached insights."""
        try:
            if insight_type:
                insights = self.insights_cache.get(insight_type, [])
            else:
                insights = []
                for insight_list in self.insights_cache.values():
                    insights.extend(insight_list)
            
            # Sort by timestamp (newest first) and limit
            insights.sort(key=lambda x: x.timestamp, reverse=True)
            return insights[:limit]
            
        except Exception as e:
            self.logger.error(f"Error getting cached insights: {e}")
            return []
    
    def clear_cache(self):
        """Clear the insights cache."""
        self.insights_cache.clear()
        self.logger.info("Analytics cache cleared")
    
    def get_analytics_stats(self) -> Dict[str, Any]:
        """Get analytics service statistics."""
        total_insights = sum(len(insights) for insights in self.insights_cache.values())
        
        return {
            "total_cached_insights": total_insights,
            "insight_categories": list(self.insights_cache.keys()),
            "cache_size_by_category": {
                category: len(insights)
                for category, insights in self.insights_cache.items()
            },
            "service_status": "active",
            "timestamp": utc_now()
        }
