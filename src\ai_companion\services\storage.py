"""
Storage Service for the AI Companion System.
Handles database operations, Redis caching, and data persistence.
"""

import asyncio
import logging
import json
import pickle
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Union
from pathlib import Path

import redis.asyncio as redis
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy import Column, String, DateTime, Text, Float, Integer, Boolean, JSON

from ..config.settings import settings
from ..core.models import MemoryEntry, PersonalMemory, UniversalMemory, UserProfile, EmotionalState
from ..utils.helpers import generate_id, utc_now


# SQLAlchemy Base
Base = declarative_base()


class UserProfileDB(Base):
    """SQLAlchemy model for user profiles."""
    __tablename__ = "user_profiles"
    
    user_id = Column(String, primary_key=True)
    name = Column(String)
    preferences = Column(JSON)
    personality_traits = Column(JSON)
    communication_style = Column(JSON)
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)
    is_active = Column(Boolean, default=True)


class PersonalMemoryDB(Base):
    """SQLAlchemy model for personal memories."""
    __tablename__ = "personal_memories"
    
    memory_id = Column(String, primary_key=True)
    user_id = Column(String, index=True)
    content = Column(Text)
    keywords = Column(JSON)
    emotional_context = Column(JSON)
    importance_score = Column(Float)
    interaction_type = Column(String)
    timestamp = Column(DateTime, default=utc_now)
    last_accessed = Column(DateTime, default=utc_now)
    access_count = Column(Integer, default=0)


class UniversalMemoryDB(Base):
    """SQLAlchemy model for universal memories."""
    __tablename__ = "universal_memories"
    
    memory_id = Column(String, primary_key=True)
    content = Column(Text)
    keywords = Column(JSON)
    emotional_patterns = Column(JSON)
    usage_count = Column(Integer, default=0)
    effectiveness_score = Column(Float, default=0.0)
    timestamp = Column(DateTime, default=utc_now)
    last_used = Column(DateTime, default=utc_now)


class ConversationDB(Base):
    """SQLAlchemy model for conversation logs."""
    __tablename__ = "conversations"
    
    conversation_id = Column(String, primary_key=True)
    user_id = Column(String, index=True)
    messages = Column(JSON)
    emotional_journey = Column(JSON)
    summary = Column(Text)
    start_time = Column(DateTime, default=utc_now)
    end_time = Column(DateTime)
    duration_minutes = Column(Float)


class StorageService:
    """Advanced storage service with SQL database and Redis caching."""
    
    def __init__(self):
        """Initialize the storage service."""
        self.logger = logging.getLogger(__name__)
        self.engine = None
        self.async_engine = None
        self.SessionLocal = None
        self.AsyncSessionLocal = None
        self.redis_client = None
        self._initialize_storage()
    
    def _initialize_storage(self):
        """Initialize database and Redis connections."""
        try:
            # Initialize SQLite database
            db_path = Path(settings.database_path)
            db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Sync engine for initial setup
            self.engine = create_engine(
                f"sqlite:///{db_path}",
                echo=settings.debug_mode,
                pool_pre_ping=True
            )
            
            # Async engine for operations
            self.async_engine = create_async_engine(
                f"sqlite+aiosqlite:///{db_path}",
                echo=settings.debug_mode,
                pool_pre_ping=True
            )
            
            # Session makers
            self.SessionLocal = sessionmaker(bind=self.engine)
            self.AsyncSessionLocal = async_sessionmaker(
                bind=self.async_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # Create tables
            Base.metadata.create_all(bind=self.engine)
            
            self.logger.info(f"✅ Database initialized at {db_path}")
            
            # Initialize Redis if enabled
            if settings.redis_enabled:
                self._initialize_redis()
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize storage: {e}")
            raise
    
    def _initialize_redis(self):
        """Initialize Redis connection."""
        try:
            self.redis_client = redis.Redis(
                host=settings.redis_host,
                port=settings.redis_port,
                password=settings.redis_password,
                db=settings.redis_db,
                decode_responses=True
            )
            self.logger.info("✅ Redis connection initialized")
            
        except Exception as e:
            self.logger.warning(f"⚠️ Redis initialization failed: {e}")
            self.redis_client = None
    
    async def store_user_profile(self, user_profile: UserProfile) -> bool:
        """Store or update user profile."""
        try:
            async with self.AsyncSessionLocal() as session:
                # Check if profile exists
                result = await session.execute(
                    text("SELECT user_id FROM user_profiles WHERE user_id = :user_id"),
                    {"user_id": user_profile.user_id}
                )
                existing = result.fetchone()
                
                if existing:
                    # Update existing profile
                    await session.execute(
                        text("""UPDATE user_profiles SET 
                                name = :name,
                                preferences = :preferences,
                                personality_traits = :personality_traits,
                                communication_style = :communication_style,
                                updated_at = :updated_at
                                WHERE user_id = :user_id"""),
                        {
                            "user_id": user_profile.user_id,
                            "name": user_profile.name,
                            "preferences": json.dumps(user_profile.preferences),
                            "personality_traits": json.dumps(user_profile.personality_traits),
                            "communication_style": json.dumps(user_profile.communication_style),
                            "updated_at": utc_now()
                        }
                    )
                else:
                    # Insert new profile
                    await session.execute(
                        text("""INSERT INTO user_profiles 
                                (user_id, name, preferences, personality_traits, communication_style, created_at, updated_at, is_active)
                                VALUES (:user_id, :name, :preferences, :personality_traits, :communication_style, :created_at, :updated_at, :is_active)"""),
                        {
                            "user_id": user_profile.user_id,
                            "name": user_profile.name,
                            "preferences": json.dumps(user_profile.preferences),
                            "personality_traits": json.dumps(user_profile.personality_traits),
                            "communication_style": json.dumps(user_profile.communication_style),
                            "created_at": utc_now(),
                            "updated_at": utc_now(),
                            "is_active": True
                        }
                    )
                
                await session.commit()
                
                # Cache in Redis if available
                if self.redis_client:
                    await self.redis_client.setex(
                        f"user_profile:{user_profile.user_id}",
                        3600,  # 1 hour TTL
                        json.dumps(user_profile.model_dump())
                    )
                
                return True
                
        except Exception as e:
            self.logger.error(f"Error storing user profile: {e}")
            return False
    
    async def get_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """Retrieve user profile."""
        try:
            # Try Redis cache first
            if self.redis_client:
                cached = await self.redis_client.get(f"user_profile:{user_id}")
                if cached:
                    data = json.loads(cached)
                    return UserProfile(**data)
            
            # Query database
            async with self.AsyncSessionLocal() as session:
                result = await session.execute(
                    text("SELECT * FROM user_profiles WHERE user_id = :user_id AND is_active = 1"),
                    {"user_id": user_id}
                )
                row = result.fetchone()
                
                if row:
                    return UserProfile(
                        user_id=row.user_id,
                        name=row.name,
                        preferences=json.loads(row.preferences or "{}"),
                        personality_traits=json.loads(row.personality_traits or "{}"),
                        communication_style=json.loads(row.communication_style or "{}"),
                        created_at=row.created_at,
                        updated_at=row.updated_at
                    )
                
                return None
                
        except Exception as e:
            self.logger.error(f"Error retrieving user profile: {e}")
            return None
    
    async def store_personal_memory(self, memory: PersonalMemory) -> bool:
        """Store personal memory."""
        try:
            async with self.AsyncSessionLocal() as session:
                await session.execute(
                    text("""INSERT OR REPLACE INTO personal_memories 
                            (memory_id, user_id, content, keywords, emotional_context, 
                             importance_score, interaction_type, timestamp, last_accessed, access_count)
                            VALUES (:memory_id, :user_id, :content, :keywords, :emotional_context,
                                    :importance_score, :interaction_type, :timestamp, :last_accessed, :access_count)"""),
                    {
                        "memory_id": memory.id,
                        "user_id": memory.user_id,
                        "content": memory.content,
                        "keywords": json.dumps(memory.keywords),
                        "emotional_context": json.dumps(memory.emotional_context),
                        "importance_score": memory.importance_score,
                        "interaction_type": memory.interaction_type.value if hasattr(memory.interaction_type, 'value') else str(memory.interaction_type),
                        "timestamp": memory.timestamp,
                        "last_accessed": memory.last_accessed,
                        "access_count": memory.access_count
                    }
                )
                await session.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Error storing personal memory: {e}")
            return False
    
    async def get_personal_memories(
        self,
        user_id: str,
        limit: int = 50,
        keywords: List[str] = None
    ) -> List[PersonalMemory]:
        """Retrieve personal memories for a user."""
        try:
            async with self.AsyncSessionLocal() as session:
                query = "SELECT * FROM personal_memories WHERE user_id = :user_id"
                params = {"user_id": user_id}
                
                if keywords:
                    # Simple keyword matching (could be enhanced with full-text search)
                    keyword_conditions = []
                    for i, keyword in enumerate(keywords):
                        keyword_conditions.append(f"content LIKE :keyword_{i}")
                        params[f"keyword_{i}"] = f"%{keyword}%"
                    
                    if keyword_conditions:
                        query += " AND (" + " OR ".join(keyword_conditions) + ")"
                
                query += " ORDER BY importance_score DESC, timestamp DESC LIMIT :limit"
                params["limit"] = limit
                
                result = await session.execute(text(query), params)
                rows = result.fetchall()
                
                memories = []
                for row in rows:
                    memory = PersonalMemory(
                        memory_id=row.memory_id,
                        user_id=row.user_id,
                        content=row.content,
                        keywords=json.loads(row.keywords or "[]"),
                        emotional_context=json.loads(row.emotional_context or "{}"),
                        importance_score=row.importance_score,
                        interaction_type=row.interaction_type,
                        timestamp=row.timestamp,
                        last_accessed=row.last_accessed,
                        access_count=row.access_count
                    )
                    memories.append(memory)
                
                return memories
                
        except Exception as e:
            self.logger.error(f"Error retrieving personal memories: {e}")
            return []
    
    async def store_universal_memory(self, memory: UniversalMemory) -> bool:
        """Store universal memory."""
        try:
            async with self.AsyncSessionLocal() as session:
                await session.execute(
                    text("""INSERT OR REPLACE INTO universal_memories 
                            (memory_id, content, keywords, emotional_patterns, 
                             usage_count, effectiveness_score, timestamp, last_used)
                            VALUES (:memory_id, :content, :keywords, :emotional_patterns,
                                    :usage_count, :effectiveness_score, :timestamp, :last_used)"""),
                    {
                        "memory_id": memory.memory_id,
                        "content": memory.content,
                        "keywords": json.dumps(memory.keywords),
                        "emotional_patterns": json.dumps(memory.emotional_patterns),
                        "usage_count": memory.usage_count,
                        "effectiveness_score": memory.effectiveness_score,
                        "timestamp": memory.timestamp,
                        "last_used": memory.last_used
                    }
                )
                await session.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Error storing universal memory: {e}")
            return False
    
    async def get_universal_memories(
        self,
        keywords: List[str] = None,
        limit: int = 20
    ) -> List[UniversalMemory]:
        """Retrieve universal memories."""
        try:
            async with self.AsyncSessionLocal() as session:
                query = "SELECT * FROM universal_memories"
                params = {}
                
                if keywords:
                    keyword_conditions = []
                    for i, keyword in enumerate(keywords):
                        keyword_conditions.append(f"content LIKE :keyword_{i}")
                        params[f"keyword_{i}"] = f"%{keyword}%"
                    
                    if keyword_conditions:
                        query += " WHERE (" + " OR ".join(keyword_conditions) + ")"
                
                query += " ORDER BY effectiveness_score DESC, usage_count DESC LIMIT :limit"
                params["limit"] = limit
                
                result = await session.execute(text(query), params)
                rows = result.fetchall()
                
                memories = []
                for row in rows:
                    memory = UniversalMemory(
                        memory_id=row.memory_id,
                        content=row.content,
                        keywords=json.loads(row.keywords or "[]"),
                        emotional_patterns=json.loads(row.emotional_patterns or "{}"),
                        usage_count=row.usage_count,
                        effectiveness_score=row.effectiveness_score,
                        timestamp=row.timestamp,
                        last_used=row.last_used
                    )
                    memories.append(memory)
                
                return memories
                
        except Exception as e:
            self.logger.error(f"Error retrieving universal memories: {e}")
            return []
    
    async def cache_set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """Set cache value with TTL."""
        if not self.redis_client:
            return False
        
        try:
            serialized_value = json.dumps(value) if not isinstance(value, str) else value
            await self.redis_client.setex(key, ttl, serialized_value)
            return True
        except Exception as e:
            self.logger.error(f"Error setting cache: {e}")
            return False
    
    async def cache_get(self, key: str) -> Optional[Any]:
        """Get cache value."""
        if not self.redis_client:
            return None
        
        try:
            value = await self.redis_client.get(key)
            if value:
                try:
                    return json.loads(value)
                except json.JSONDecodeError:
                    return value
            return None
        except Exception as e:
            self.logger.error(f"Error getting cache: {e}")
            return None
    
    async def cleanup_old_data(self, days_old: int = 90):
        """Clean up old data to maintain performance."""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)
            
            async with self.AsyncSessionLocal() as session:
                # Clean old conversations
                await session.execute(
                    text("DELETE FROM conversations WHERE start_time < :cutoff_date"),
                    {"cutoff_date": cutoff_date}
                )
                
                # Clean low-importance personal memories
                await session.execute(
                    text("""DELETE FROM personal_memories 
                            WHERE timestamp < :cutoff_date AND importance_score < 0.3"""),
                    {"cutoff_date": cutoff_date}
                )
                
                await session.commit()
                
            self.logger.info(f"Cleaned up data older than {days_old} days")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    async def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        try:
            async with self.AsyncSessionLocal() as session:
                stats = {}
                
                # Count records in each table
                for table in ["user_profiles", "personal_memories", "universal_memories", "conversations"]:
                    result = await session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    stats[f"{table}_count"] = result.scalar()
                
                # Database size
                db_path = Path(settings.database_path)
                if db_path.exists():
                    stats["database_size_mb"] = db_path.stat().st_size / (1024 * 1024)
                
                return stats
                
        except Exception as e:
            self.logger.error(f"Error getting storage stats: {e}")
            return {}
    
    async def close(self):
        """Close storage connections."""
        try:
            if self.async_engine:
                await self.async_engine.dispose()
            
            if self.redis_client:
                await self.redis_client.close()
            
            self.logger.info("Storage connections closed")
            
        except Exception as e:
            self.logger.error(f"Error closing storage: {e}")
