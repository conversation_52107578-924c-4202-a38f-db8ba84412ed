"""
Conversation Service for the AI Companion System.
Orchestrates conversation flow, memory integration, and emotional intelligence.
"""

import asyncio
import time
import uuid
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from collections import defaultdict, deque

from .models import (
    EmotionType, InteractionType, MemoryType,
    EmotionalState, ConversationMessage, Conversation
)
from .memory import MemoryService
from .emotions import EmotionalIntelligenceService
from ..services.gemini import GeminiService
from ..utils.helpers import generate_id, utc_now
from ..utils.monitoring import performance_monitor
from ..config.settings import settings


class ConversationContext:
    """Context for an active conversation."""
    
    def __init__(self, user_id: str, conversation_id: str):
        self.user_id = user_id
        self.conversation_id = conversation_id
        self.messages: List[ConversationMessage] = []
        self.current_topics: List[str] = []
        self.emotional_state = EmotionalState()
        self.start_time = utc_now()
        self.last_activity = utc_now()
        self.context_data: Dict[str, Any] = {}


class ConversationService:
    """Main conversation service orchestrating all AI companion capabilities."""
    
    def __init__(
        self,
        memory_service: MemoryService,
        emotional_intelligence: EmotionalIntelligenceService,
        gemini_service: GeminiService
    ):
        """Initialize the conversation service."""
        self.memory_service = memory_service
        self.emotional_intelligence = emotional_intelligence
        self.gemini_service = gemini_service
        
        # Active conversations (in-memory for performance)
        self.active_conversations: Dict[str, ConversationContext] = {}
        
        # Response cache for performance
        self.response_cache: Dict[str, str] = {}
        self.cache_size_limit = settings.cache_size
        
        # Performance tracking
        self.total_conversations = 0
        self.total_messages = 0
        
        self.logger = logging.getLogger(__name__)
    
    async def start_conversation(self, user_id: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Start a new conversation."""
        conversation_id = generate_id()
        
        # Create conversation context
        conv_context = ConversationContext(user_id, conversation_id)
        if context:
            conv_context.context_data.update(context)
        
        # Store in active conversations
        self.active_conversations[conversation_id] = conv_context
        self.total_conversations += 1
        
        self.logger.info(f"Started conversation {conversation_id} for user {user_id}")
        return conversation_id
    
    async def process_message(
        self,
        user_id: str,
        message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process a user message and generate a response."""
        start_time = time.time()

        # Input validation
        if not user_id or not user_id.strip():
            raise ValueError("User ID cannot be empty")
        if not message or not message.strip():
            raise ValueError("Message cannot be empty")
        if len(message) > settings.max_context_length:
            raise ValueError(f"Message too long. Maximum length is {settings.max_context_length} characters")

        try:
            # Get or create conversation
            conversation_id = await self._get_or_create_conversation(user_id, context)
            conv_context = self.active_conversations[conversation_id]
            
            # Update activity timestamp
            conv_context.last_activity = utc_now()
            
            # Analyze emotion
            emotional_state = await self.emotional_intelligence.analyze_emotion(
                user_id=user_id,
                text=message,
                context=conv_context.context_data
            )
            
            # Update conversation emotional state
            conv_context.emotional_state = emotional_state
            
            # Create user message
            user_msg = ConversationMessage(
                user_id=user_id,
                role="user",
                content=message,
                emotion=emotional_state.primary_emotion,
                confidence=emotional_state.confidence,
                context=context or {}
            )
            conv_context.messages.append(user_msg)
            
            # Retrieve relevant memories
            relevant_memories = await self.memory_service.retrieve_memories(
                user_id=user_id,
                query=message,
                limit=5
            )
            
            # Generate AI response
            ai_response = await self._generate_response(
                conv_context, message, emotional_state, relevant_memories
            )
            
            # Create assistant message
            assistant_msg = ConversationMessage(
                user_id=user_id,
                role="assistant",
                content=ai_response,
                context={"relevant_memories": len(relevant_memories)}
            )
            conv_context.messages.append(assistant_msg)
            
            # Store memory of this interaction
            await self._store_interaction_memory(
                user_id, message, ai_response, emotional_state
            )
            
            # Update topics
            await self._update_conversation_topics(conv_context, message)
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Record performance metrics
            performance_monitor.record_response_time(processing_time, user_id)
            self.total_messages += 1
            
            # Assess risk level
            risk_level = await self.emotional_intelligence.assess_risk(
                user_id, message, emotional_state
            )
            
            response_data = {
                "conversation_id": conversation_id,
                "response": ai_response,
                "emotion_detected": emotional_state.primary_emotion.value,
                "confidence": emotional_state.confidence,
                "risk_level": risk_level.value if risk_level else "low",
                "processing_time": processing_time,
                "relevant_memories": len(relevant_memories)
            }
            
            self.logger.info(f"Processed message for user {user_id} in {processing_time:.3f}s")
            return response_data
            
        except ValueError as e:
            self.logger.error(f"Validation error processing message for user {user_id}: {e}")
            return {
                "error": "Invalid input provided. Please check your message and try again.",
                "response": "I noticed there might be an issue with your message format. Could you try rephrasing it?",
                "processing_time": time.time() - start_time
            }
        except ConnectionError as e:
            self.logger.error(f"Connection error processing message for user {user_id}: {e}")
            return {
                "error": "Connection issue encountered. Please try again in a moment.",
                "response": "I'm having trouble connecting to my services right now. Please try again shortly.",
                "processing_time": time.time() - start_time
            }
        except Exception as e:
            self.logger.error(f"Unexpected error processing message for user {user_id}: {e}")
            return {
                "error": "I'm having trouble processing that right now. Could you try again?",
                "response": "I apologize, but I encountered an issue. Please try rephrasing your message.",
                "processing_time": time.time() - start_time
            }
    
    async def get_conversation_history(
        self,
        conversation_id: str,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Get conversation history."""
        conv_context = self.active_conversations.get(conversation_id)
        if not conv_context:
            return []
        
        messages = conv_context.messages[-limit:]
        return [
            {
                "role": msg.role,
                "content": msg.content,
                "timestamp": msg.timestamp.isoformat(),
                "emotion": msg.emotion.value if msg.emotion else None,
                "confidence": msg.confidence
            }
            for msg in messages
        ]
    
    async def end_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """End a conversation and clean up."""
        conv_context = self.active_conversations.get(conversation_id)
        if not conv_context:
            return {"error": "Conversation not found"}
        
        try:
            # Generate conversation summary
            summary = await self._generate_conversation_summary(conv_context)
            
            # Store conversation record
            conversation = Conversation(
                id=conversation_id,
                user_id=conv_context.user_id,
                messages=conv_context.messages,
                end_time=utc_now(),
                summary=summary,
                topics=conv_context.current_topics,
                emotions=[msg.emotion for msg in conv_context.messages if msg.emotion]
            )
            
            # Clean up active conversation
            del self.active_conversations[conversation_id]
            
            self.logger.info(f"Ended conversation {conversation_id}")
            
            return {
                "conversation_id": conversation_id,
                "summary": summary,
                "total_messages": len(conv_context.messages),
                "duration": (utc_now() - conv_context.start_time).total_seconds(),
                "topics": conv_context.current_topics
            }
            
        except Exception as e:
            self.logger.error(f"Error ending conversation {conversation_id}: {e}")
            return {"error": "Failed to end conversation properly"}
    
    async def _get_or_create_conversation(
        self,
        user_id: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Get existing conversation or create a new one."""
        session_id = context.get("session_id") if context else None
        
        # Look for existing conversation with same session
        if session_id:
            for conv_id, conv_context in self.active_conversations.items():
                if (conv_context.user_id == user_id and 
                    conv_context.context_data.get("session_id") == session_id):
                    return conv_id
        
        # Create new conversation
        return await self.start_conversation(user_id, context)
    
    async def _generate_response(
        self,
        conv_context: ConversationContext,
        message: str,
        emotional_state: EmotionalState,
        relevant_memories: List[Any]
    ) -> str:
        """Generate AI response using context and memories."""
        
        # Check cache first
        cache_key = self._generate_cache_key(conv_context.user_id, message, emotional_state)
        if cache_key in self.response_cache:
            return self.response_cache[cache_key]
        
        # Build context for AI
        context_parts = []
        
        # Add recent conversation history
        recent_messages = conv_context.messages[-5:]  # Last 5 messages
        if recent_messages:
            context_parts.append("Recent conversation:")
            for msg in recent_messages:
                context_parts.append(f"{msg.role}: {msg.content}")
        
        # Add relevant memories
        if relevant_memories:
            context_parts.append("\nRelevant memories:")
            for memory in relevant_memories[:3]:  # Top 3 memories
                context_parts.append(f"- {memory.content}")
        
        # Add emotional context
        emotion_context = f"\nUser's emotional state: {emotional_state.primary_emotion.value}"
        if emotional_state.intensity > 0.7:
            emotion_context += f" (high intensity: {emotional_state.intensity:.2f})"
        context_parts.append(emotion_context)
        
        # Generate therapeutic response if needed
        if emotional_state.primary_emotion in [EmotionType.SADNESS, EmotionType.ANXIETY, EmotionType.ANGER]:
            therapeutic_response = await self.emotional_intelligence.generate_therapeutic_response(
                conv_context.user_id, message, emotional_state
            )
            if therapeutic_response:
                context_parts.append(f"\nTherapeutic guidance: {therapeutic_response}")
        
        # Build prompt
        context_text = "\n".join(context_parts)
        prompt = f"""You are a compassionate AI companion focused on emotional support and mental health. 

Context:
{context_text}

Current message: {message}

Respond with empathy, understanding, and appropriate support. Keep responses concise but meaningful. If the user seems to be in crisis, prioritize their safety and well-being."""
        
        try:
            # Generate response
            response = await self.gemini_service.generate_response(prompt)
            
            # Cache the response
            if len(self.response_cache) < self.cache_size_limit:
                self.response_cache[cache_key] = response
            elif len(self.response_cache) >= self.cache_size_limit:
                # Remove oldest entries
                oldest_keys = list(self.response_cache.keys())[:100]
                for key in oldest_keys:
                    del self.response_cache[key]
                self.response_cache[cache_key] = response
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error generating response: {e}")
            return "I'm here to listen and support you. Could you tell me more about what's on your mind?"
    
    async def _store_interaction_memory(
        self,
        user_id: str,
        user_message: str,
        ai_response: str,
        emotional_state: EmotionalState
    ) -> None:
        """Store memory of this interaction."""
        try:
            memory_content = f"User said: {user_message}\nAI responded: {ai_response}"
            
            await self.memory_service.store_memory(
                user_id=user_id,
                content=memory_content,
                interaction_type=InteractionType.CONVERSATION,
                emotion=emotional_state.primary_emotion,
                importance=min(0.8, 0.5 + emotional_state.intensity * 0.3)
            )
        except Exception as e:
            self.logger.error(f"Error storing interaction memory: {e}")
    
    async def _update_conversation_topics(self, conv_context: ConversationContext, message: str) -> None:
        """Update conversation topics based on message content."""
        # Simple keyword extraction (can be enhanced with NLP)
        words = message.lower().split()
        keywords = [word for word in words if len(word) > 4][:3]  # Top 3 keywords
        
        for keyword in keywords:
            if keyword not in conv_context.current_topics:
                conv_context.current_topics.append(keyword)
        
        # Keep only recent topics
        conv_context.current_topics = conv_context.current_topics[-10:]
    
    async def _generate_conversation_summary(self, conv_context: ConversationContext) -> str:
        """Generate a summary of the conversation."""
        if not conv_context.messages:
            return "No messages in conversation"
        
        try:
            # Build summary prompt
            messages_text = "\n".join([
                f"{msg.role}: {msg.content}"
                for msg in conv_context.messages[-10:]  # Last 10 messages
            ])
            
            prompt = f"""Summarize this conversation in 1-2 sentences, focusing on the main topics and emotional themes:

{messages_text}

Summary:"""
            
            summary = await self.gemini_service.generate_response(prompt)
            return summary.strip()
            
        except Exception as e:
            self.logger.error(f"Error generating conversation summary: {e}")
            return f"Conversation with {len(conv_context.messages)} messages about {', '.join(conv_context.current_topics[:3])}"
    
    def _generate_cache_key(self, user_id: str, message: str, emotional_state: EmotionalState) -> str:
        """Generate cache key for response caching."""
        # Simple cache key based on message content and emotion
        emotion_key = emotional_state.primary_emotion.value
        message_hash = hash(message.lower().strip())
        return f"{user_id}:{emotion_key}:{message_hash}"
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get conversation service performance metrics."""
        cache_hit_rate = 0.0
        if self.total_messages > 0:
            cache_hits = sum(1 for _ in self.response_cache.keys())
            cache_hit_rate = cache_hits / self.total_messages
        
        return {
            "total_conversations": self.total_conversations,
            "total_messages": self.total_messages,
            "active_conversations": len(self.active_conversations),
            "cache_size": len(self.response_cache),
            "cache_hit_rate": cache_hit_rate
        }
    
    async def cleanup_inactive_conversations(self, max_age_hours: int = 24) -> None:
        """Clean up inactive conversations."""
        current_time = utc_now()
        inactive_conversations = []
        
        for conv_id, conv_context in self.active_conversations.items():
            age_hours = (current_time - conv_context.last_activity).total_seconds() / 3600
            if age_hours > max_age_hours:
                inactive_conversations.append(conv_id)
        
        for conv_id in inactive_conversations:
            await self.end_conversation(conv_id)
        
        self.logger.info(f"Cleaned up {len(inactive_conversations)} inactive conversations")
