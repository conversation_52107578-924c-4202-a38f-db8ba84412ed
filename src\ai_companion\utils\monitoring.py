"""
Performance monitoring and metrics collection for the AI Companion System.
"""

import time
import psutil
import threading
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from collections import defaultdict, deque
from dataclasses import dataclass, field

from ..config.settings import settings


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    response_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    memory_usage: deque = field(default_factory=lambda: deque(maxlen=100))
    cpu_usage: deque = field(default_factory=lambda: deque(maxlen=100))
    cache_hit_rates: deque = field(default_factory=lambda: deque(maxlen=100))
    error_counts: Dict[str, int] = field(default_factory=dict)
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    start_time: float = field(default_factory=time.time)


class PerformanceMonitor:
    """Monitor system performance and collect metrics."""
    
    def __init__(self):
        self.metrics = PerformanceMetrics()
        self.user_metrics: Dict[str, PerformanceMetrics] = defaultdict(PerformanceMetrics)
        self._lock = threading.Lock()
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        
        # Start monitoring
        self.start_monitoring()
    
    def start_monitoring(self):
        """Start background monitoring."""
        if not self._monitoring:
            self._monitoring = True
            self._monitor_thread = threading.Thread(target=self._monitor_system, daemon=True)
            self._monitor_thread.start()
    
    def stop_monitoring(self):
        """Stop background monitoring."""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
    
    def _monitor_system(self):
        """Background system monitoring."""
        while self._monitoring:
            try:
                # Collect system metrics
                memory_percent = psutil.virtual_memory().percent
                cpu_percent = psutil.cpu_percent(interval=1)
                
                with self._lock:
                    self.metrics.memory_usage.append(memory_percent)
                    self.metrics.cpu_usage.append(cpu_percent)
                
                time.sleep(10)  # Monitor every 10 seconds
                
            except Exception as e:
                # Log error but continue monitoring
                print(f"Error in system monitoring: {e}")
                time.sleep(30)
    
    def record_response_time(self, response_time: float, user_id: str = None):
        """Record response time metric."""
        with self._lock:
            self.metrics.response_times.append(response_time)
            self.metrics.total_requests += 1
            
            if response_time <= settings.target_response_time:
                self.metrics.successful_requests += 1
            else:
                self.metrics.failed_requests += 1
            
            if user_id:
                user_metrics = self.user_metrics[user_id]
                user_metrics.response_times.append(response_time)
                user_metrics.total_requests += 1
    
    def record_cache_hit_rate(self, hit_rate: float, user_id: str = None):
        """Record cache hit rate metric."""
        with self._lock:
            self.metrics.cache_hit_rates.append(hit_rate)
            
            if user_id:
                self.user_metrics[user_id].cache_hit_rates.append(hit_rate)
    
    def record_error(self, error_type: str, user_id: str = None):
        """Record error occurrence."""
        with self._lock:
            self.metrics.error_counts[error_type] = self.metrics.error_counts.get(error_type, 0) + 1
            
            if user_id:
                user_metrics = self.user_metrics[user_id]
                user_metrics.error_counts[error_type] = user_metrics.error_counts.get(error_type, 0) + 1
    
    def record_metric(self, metric_name: str, value: float, user_id: str = None):
        """Record a custom metric."""
        # For now, just store in a simple way
        # In production, you might want to use a proper metrics system like Prometheus
        pass
    
    def get_metrics(self, user_id: str = None) -> Dict[str, Any]:
        """Get current performance metrics."""
        with self._lock:
            if user_id and user_id in self.user_metrics:
                metrics = self.user_metrics[user_id]
            else:
                metrics = self.metrics
            
            # Calculate averages
            avg_response_time = sum(metrics.response_times) / len(metrics.response_times) if metrics.response_times else 0
            avg_memory_usage = sum(metrics.memory_usage) / len(metrics.memory_usage) if metrics.memory_usage else 0
            avg_cpu_usage = sum(metrics.cpu_usage) / len(metrics.cpu_usage) if metrics.cpu_usage else 0
            avg_cache_hit_rate = sum(metrics.cache_hit_rates) / len(metrics.cache_hit_rates) if metrics.cache_hit_rates else 0
            
            # Calculate success rate
            success_rate = (metrics.successful_requests / metrics.total_requests * 100) if metrics.total_requests > 0 else 0
            
            # Calculate uptime
            uptime = time.time() - metrics.start_time
            
            return {
                "response_time": {
                    "average": avg_response_time,
                    "latest": list(metrics.response_times)[-10:] if metrics.response_times else [],
                    "target": settings.target_response_time,
                    "meets_target": avg_response_time <= settings.target_response_time
                },
                "system": {
                    "memory_usage": avg_memory_usage,
                    "cpu_usage": avg_cpu_usage,
                    "uptime": uptime
                },
                "cache": {
                    "hit_rate": avg_cache_hit_rate,
                    "target": settings.target_cache_hit_rate,
                    "meets_target": avg_cache_hit_rate >= settings.target_cache_hit_rate
                },
                "requests": {
                    "total": metrics.total_requests,
                    "successful": metrics.successful_requests,
                    "failed": metrics.failed_requests,
                    "success_rate": success_rate
                },
                "errors": dict(metrics.error_counts),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get system health status."""
        metrics = self.get_metrics()
        
        # Determine health status
        health_issues = []
        
        if not metrics["response_time"]["meets_target"]:
            health_issues.append("Response time above target")
        
        if not metrics["cache"]["meets_target"]:
            health_issues.append("Cache hit rate below target")
        
        if metrics["system"]["memory_usage"] > 90:
            health_issues.append("High memory usage")
        
        if metrics["system"]["cpu_usage"] > 90:
            health_issues.append("High CPU usage")
        
        if metrics["requests"]["success_rate"] < 95:
            health_issues.append("Low success rate")
        
        # Determine overall status
        if not health_issues:
            status = "healthy"
        elif len(health_issues) <= 2:
            status = "warning"
        else:
            status = "critical"
        
        return {
            "status": status,
            "issues": health_issues,
            "metrics": metrics,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    
    def reset_metrics(self, user_id: str = None):
        """Reset metrics for a user or globally."""
        with self._lock:
            if user_id and user_id in self.user_metrics:
                self.user_metrics[user_id] = PerformanceMetrics()
            else:
                self.metrics = PerformanceMetrics()
    
    def get_user_list(self) -> List[str]:
        """Get list of users with metrics."""
        with self._lock:
            return list(self.user_metrics.keys())


class AlertManager:
    """Manage system alerts and notifications."""
    
    def __init__(self, performance_monitor: PerformanceMonitor):
        self.performance_monitor = performance_monitor
        self.alerts: List[Dict[str, Any]] = []
        self._lock = threading.Lock()
    
    def check_alerts(self) -> List[Dict[str, Any]]:
        """Check for alert conditions and return active alerts."""
        health_status = self.performance_monitor.get_health_status()
        
        new_alerts = []
        
        if health_status["status"] == "critical":
            new_alerts.append({
                "level": "critical",
                "message": "System health is critical",
                "details": health_status["issues"],
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        elif health_status["status"] == "warning":
            new_alerts.append({
                "level": "warning",
                "message": "System health warning",
                "details": health_status["issues"],
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        
        with self._lock:
            self.alerts.extend(new_alerts)
            # Keep only last 100 alerts
            self.alerts = self.alerts[-100:]
        
        return new_alerts
    
    def get_alerts(self, level: str = None) -> List[Dict[str, Any]]:
        """Get alerts, optionally filtered by level."""
        with self._lock:
            if level:
                return [alert for alert in self.alerts if alert["level"] == level]
            return self.alerts.copy()
    
    def clear_alerts(self):
        """Clear all alerts."""
        with self._lock:
            self.alerts.clear()


# Global performance monitor instance
performance_monitor = PerformanceMonitor()
alert_manager = AlertManager(performance_monitor)
