# AI Companion System - Core Dependencies
# Generated from pyproject.toml for easier installation

# Core AI and ML libraries
google-generativeai>=0.3.0,<1.0.0
sentence-transformers>=2.2.0,<3.0.0
torch>=2.0.0,<3.0.0
transformers>=4.30.0,<5.0.0

# Web framework and API
fastapi>=0.100.0,<1.0.0
uvicorn[standard]>=0.23.0,<1.0.0
gradio>=4.0.0,<5.0.0

# Database and caching
redis>=4.5.0,<6.0.0
sqlalchemy>=2.0.0,<3.0.0
alembic>=1.11.0,<2.0.0

# Data processing
pandas>=2.0.0,<3.0.0
numpy>=1.24.0,<2.0.0
scikit-learn>=1.3.0,<2.0.0

# Configuration and utilities
python-dotenv>=1.0.0,<2.0.0
pydantic>=2.0.0,<3.0.0
pydantic-settings>=2.0.0,<3.0.0
httpx>=0.24.0,<1.0.0
requests>=2.31.0,<3.0.0
psutil>=5.9.0,<6.0.0
cryptography>=41.0.0,<42.0.0
python-multipart>=0.0.6,<1.0.0
python-dateutil>=2.8.0,<3.0.0
pytz>=2023.3
