"""
Core Crisis Detection Service.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from collections import defaultdict

from .models import CrisisType, CrisisEvent, InterventionType
from .detector import CrisisDetector
from .intervention import InterventionManager
from .resources import ResourceManager
from ...core.models import MentalHealthRisk, utc_now, generate_id
from ...core.emotions import EmotionalIntelligenceService


class CrisisDetectionService:
    """Main crisis detection and intervention service."""
    
    def __init__(self, emotional_intelligence: EmotionalIntelligenceService):
        """Initialize crisis detection service."""
        self.logger = logging.getLogger(__name__)
        self.emotional_intelligence = emotional_intelligence
        
        # Initialize components
        self.detector = CrisisDetector(emotional_intelligence)
        self.intervention_manager = InterventionManager(emotional_intelligence)
        self.resource_manager = ResourceManager()
        
        # Crisis tracking
        self.crisis_events: Dict[str, CrisisEvent] = {}
        self.user_crisis_history: Dict[str, List[str]] = defaultdict(list)
        
        # Statistics
        self.crisis_stats = {
            "total_crises_detected": 0,
            "interventions_triggered": 0,
            "follow_ups_scheduled": 0,
            "crisis_types": defaultdict(int)
        }
        
        self.logger.info("✅ Crisis Detection Service initialized")
    
    async def analyze_message(self, message: str, user_id: str) -> Dict[str, Any]:
        """Analyze message for crisis indicators and trigger interventions if needed."""
        try:
            # Detect crisis patterns
            crisis_analysis = await self.detector.detect_crisis(message, user_id)
            
            if crisis_analysis.get("crisis_detected"):
                # Create crisis event
                crisis_event = await self._create_crisis_event(
                    user_id, message, crisis_analysis
                )
                
                # Handle intervention if needed
                intervention_result = None
                if crisis_analysis.get("requires_immediate_action"):
                    intervention_result = await self.intervention_manager.handle_crisis_intervention(
                        user_id, crisis_event
                    )
                
                # Get relevant resources
                resources = self.resource_manager.get_relevant_resources(
                    crisis_analysis.get("crisis_type")
                )
                
                # Update statistics
                self._update_crisis_stats(crisis_analysis)
                
                return {
                    "crisis_detected": True,
                    "crisis_event": {
                        "event_id": crisis_event.event_id,
                        "crisis_type": crisis_event.crisis_type.value if crisis_event.crisis_type else None,
                        "risk_level": crisis_event.risk_level.value,
                        "crisis_score": crisis_event.crisis_score
                    },
                    "intervention": intervention_result,
                    "resources": resources,
                    "requires_follow_up": self._requires_follow_up(crisis_analysis.get("crisis_type")),
                    "safety_planning_needed": self._needs_safety_planning(crisis_analysis.get("crisis_type")),
                    "timestamp": utc_now()
                }
            else:
                return {
                    "crisis_detected": False,
                    "message": "No crisis indicators detected",
                    "timestamp": utc_now()
                }
                
        except Exception as e:
            self.logger.error(f"Error analyzing message for crisis: {e}")
            return {
                "crisis_detected": False,
                "error": str(e),
                "timestamp": utc_now()
            }
    
    async def _create_crisis_event(
        self,
        user_id: str,
        message: str,
        crisis_analysis: Dict[str, Any]
    ) -> CrisisEvent:
        """Create a crisis event from analysis results."""
        event_id = generate_id()
        
        # Determine crisis type
        crisis_type = None
        if crisis_analysis.get("crisis_type"):
            try:
                crisis_type = CrisisType(crisis_analysis["crisis_type"])
            except ValueError:
                self.logger.warning(f"Unknown crisis type: {crisis_analysis['crisis_type']}")
        
        # Create crisis event
        crisis_event = CrisisEvent(
            event_id=event_id,
            user_id=user_id,
            crisis_type=crisis_type,
            risk_level=MentalHealthRisk(crisis_analysis.get("crisis_level", "low")),
            crisis_score=crisis_analysis.get("crisis_score", 0.0),
            message_content=message,
            detected_patterns=crisis_analysis.get("detected_patterns", []),
            intervention_triggered=False,
            intervention_type=None,
            resources_provided=[],
            timestamp=utc_now(),
            follow_up_scheduled=False
        )
        
        # Store crisis event
        self.crisis_events[event_id] = crisis_event
        self.user_crisis_history[user_id].append(event_id)
        
        self.logger.info(f"Crisis event created: {event_id} for user {user_id}")
        
        return crisis_event
    
    def _update_crisis_stats(self, crisis_analysis: Dict[str, Any]):
        """Update crisis detection statistics."""
        self.crisis_stats["total_crises_detected"] += 1
        
        if crisis_analysis.get("requires_immediate_action"):
            self.crisis_stats["interventions_triggered"] += 1
        
        crisis_type = crisis_analysis.get("crisis_type")
        if crisis_type:
            self.crisis_stats["crisis_types"][crisis_type] += 1
    
    def _requires_follow_up(self, crisis_type: Optional[str]) -> bool:
        """Check if crisis type requires follow-up."""
        follow_up_types = [
            "suicidal_ideation", "self_harm", "severe_depression", 
            "substance_abuse", "domestic_violence", "eating_disorder"
        ]
        return crisis_type in follow_up_types
    
    def _needs_safety_planning(self, crisis_type: Optional[str]) -> bool:
        """Check if crisis type needs safety planning."""
        safety_planning_types = [
            "suicidal_ideation", "self_harm", "domestic_violence"
        ]
        return crisis_type in safety_planning_types
    
    async def get_user_crisis_history(self, user_id: str) -> List[Dict[str, Any]]:
        """Get crisis history for a user."""
        try:
            event_ids = self.user_crisis_history.get(user_id, [])
            crisis_history = []
            
            for event_id in event_ids:
                if event_id in self.crisis_events:
                    event = self.crisis_events[event_id]
                    crisis_history.append({
                        "event_id": event.event_id,
                        "crisis_type": event.crisis_type.value if event.crisis_type else None,
                        "risk_level": event.risk_level.value,
                        "crisis_score": event.crisis_score,
                        "intervention_triggered": event.intervention_triggered,
                        "timestamp": event.timestamp,
                        "resolved": event.resolved
                    })
            
            return crisis_history
            
        except Exception as e:
            self.logger.error(f"Error getting crisis history for user {user_id}: {e}")
            return []
    
    def get_crisis_summary(self, user_id: str) -> Dict[str, Any]:
        """Get crisis summary for a user."""
        try:
            crisis_history = self.user_crisis_history.get(user_id, [])
            
            if not crisis_history:
                return {
                    "total_crises": 0,
                    "last_crisis": None,
                    "risk_level": "low",
                    "active_interventions": 0
                }
            
            # Get recent crisis events
            recent_events = [
                self.crisis_events[event_id] 
                for event_id in crisis_history[-5:] 
                if event_id in self.crisis_events
            ]
            
            # Calculate summary statistics
            total_crises = len(crisis_history)
            last_crisis = recent_events[-1] if recent_events else None
            
            # Determine current risk level
            if recent_events:
                recent_scores = [event.crisis_score for event in recent_events]
                avg_recent_score = sum(recent_scores) / len(recent_scores)
                
                if avg_recent_score >= 0.7:
                    current_risk = "high"
                elif avg_recent_score >= 0.4:
                    current_risk = "moderate"
                else:
                    current_risk = "low"
            else:
                current_risk = "low"
            
            # Count active interventions
            active_interventions = sum(
                1 for event in recent_events 
                if event.intervention_triggered and not event.resolved
            )
            
            return {
                "total_crises": total_crises,
                "last_crisis": {
                    "timestamp": last_crisis.timestamp,
                    "crisis_type": last_crisis.crisis_type.value if last_crisis.crisis_type else None,
                    "risk_level": last_crisis.risk_level.value
                } if last_crisis else None,
                "current_risk_level": current_risk,
                "active_interventions": active_interventions,
                "recent_crisis_types": [
                    event.crisis_type.value for event in recent_events 
                    if event.crisis_type
                ]
            }
            
        except Exception as e:
            self.logger.error(f"Error getting crisis summary for user {user_id}: {e}")
            return {"error": str(e)}
    
    def get_system_crisis_stats(self) -> Dict[str, Any]:
        """Get system-wide crisis statistics."""
        return {
            **self.crisis_stats,
            "active_crisis_events": len([
                event for event in self.crisis_events.values() 
                if not event.resolved
            ]),
            "total_users_with_crises": len(self.user_crisis_history),
            "intervention_stats": self.intervention_manager.get_intervention_stats(),
            "timestamp": utc_now()
        }
    
    async def resolve_crisis_event(self, event_id: str):
        """Mark a crisis event as resolved."""
        if event_id in self.crisis_events:
            self.crisis_events[event_id].resolved = True
            self.intervention_manager.resolve_intervention(event_id)
            self.logger.info(f"Crisis event {event_id} marked as resolved")
        else:
            self.logger.warning(f"Crisis event {event_id} not found")
    
    def clear_resolved_events(self):
        """Clear resolved crisis events to free memory."""
        resolved_events = [
            event_id for event_id, event in self.crisis_events.items()
            if event.resolved
        ]
        
        for event_id in resolved_events:
            del self.crisis_events[event_id]
        
        self.logger.info(f"Cleared {len(resolved_events)} resolved crisis events")
