"""
Crisis Resource Management.
"""

import logging
from typing import Dict, List, Optional, Any

from .models import CrisisType, CrisisResource


class ResourceManager:
    """Manages crisis support resources and referrals."""
    
    def __init__(self):
        """Initialize resource manager."""
        self.logger = logging.getLogger(__name__)
        self.resources = self._initialize_crisis_resources()
        
    def _initialize_crisis_resources(self) -> Dict[str, List[Dict[str, str]]]:
        """Initialize crisis support resources."""
        return {
            "suicide_prevention_hotline": [
                {
                    "name": "National Suicide Prevention Lifeline",
                    "contact": "988",
                    "description": "24/7 crisis support for suicidal thoughts",
                    "availability": "24/7",
                    "type": "phone"
                },
                {
                    "name": "Crisis Text Line",
                    "contact": "Text HOME to 741741",
                    "description": "24/7 text-based crisis support",
                    "availability": "24/7",
                    "type": "text"
                }
            ],
            "emergency_services": [
                {
                    "name": "Emergency Services",
                    "contact": "911",
                    "description": "Immediate emergency response",
                    "availability": "24/7",
                    "type": "emergency"
                }
            ],
            "mental_health_services": [
                {
                    "name": "SAMHSA National Helpline",
                    "contact": "1-************",
                    "description": "Treatment referral and information service",
                    "availability": "24/7",
                    "type": "phone"
                },
                {
                    "name": "Psychology Today Therapist Finder",
                    "contact": "https://www.psychologytoday.com/us/therapists",
                    "description": "Find mental health professionals in your area",
                    "availability": "Online",
                    "type": "website"
                }
            ],
            "crisis_text_line": [
                {
                    "name": "Crisis Text Line",
                    "contact": "Text HOME to 741741",
                    "description": "Free, 24/7 crisis support via text",
                    "availability": "24/7",
                    "type": "text"
                }
            ],
            "self_harm_support": [
                {
                    "name": "Self-Injury Outreach & Support",
                    "contact": "https://sioutreach.org/",
                    "description": "Support and resources for self-harm recovery",
                    "availability": "Online",
                    "type": "website"
                },
                {
                    "name": "To Write Love on Her Arms",
                    "contact": "https://twloha.com/",
                    "description": "Hope and help for people struggling with self-harm",
                    "availability": "Online",
                    "type": "website"
                }
            ],
            "domestic_violence_hotline": [
                {
                    "name": "National Domestic Violence Hotline",
                    "contact": "**************",
                    "description": "24/7 support for domestic violence survivors",
                    "availability": "24/7",
                    "type": "phone"
                },
                {
                    "name": "National Domestic Violence Hotline Chat",
                    "contact": "https://www.thehotline.org/",
                    "description": "Online chat support for domestic violence",
                    "availability": "24/7",
                    "type": "chat"
                }
            ],
            "addiction_services": [
                {
                    "name": "SAMHSA National Helpline",
                    "contact": "1-************",
                    "description": "Treatment referral for substance abuse",
                    "availability": "24/7",
                    "type": "phone"
                },
                {
                    "name": "Narcotics Anonymous",
                    "contact": "https://www.na.org/",
                    "description": "Support groups for addiction recovery",
                    "availability": "Varies",
                    "type": "website"
                }
            ],
            "eating_disorder_support": [
                {
                    "name": "National Eating Disorders Association",
                    "contact": "**************",
                    "description": "Support and resources for eating disorders",
                    "availability": "Business hours",
                    "type": "phone"
                },
                {
                    "name": "NEDA Screening Tool",
                    "contact": "https://www.nationaleatingdisorders.org/screening-tool",
                    "description": "Online eating disorder screening",
                    "availability": "Online",
                    "type": "website"
                }
            ],
            "anxiety_support": [
                {
                    "name": "Anxiety and Depression Association of America",
                    "contact": "https://adaa.org/",
                    "description": "Resources and support for anxiety disorders",
                    "availability": "Online",
                    "type": "website"
                }
            ],
            "depression_support": [
                {
                    "name": "Depression and Bipolar Support Alliance",
                    "contact": "https://www.dbsalliance.org/",
                    "description": "Support and resources for depression",
                    "availability": "Online",
                    "type": "website"
                }
            ],
            "therapy_referral": [
                {
                    "name": "Psychology Today",
                    "contact": "https://www.psychologytoday.com/us/therapists",
                    "description": "Find therapists and mental health professionals",
                    "availability": "Online",
                    "type": "website"
                },
                {
                    "name": "Open Path Collective",
                    "contact": "https://openpathcollective.org/",
                    "description": "Affordable therapy options",
                    "availability": "Online",
                    "type": "website"
                }
            ]
        }
    
    def get_relevant_resources(self, crisis_type: Optional[str]) -> List[Dict[str, str]]:
        """Get relevant crisis resources based on crisis type."""
        if not crisis_type:
            # Return general mental health resources
            return self.resources.get("mental_health_services", [])
        
        # Map crisis types to resource categories
        resource_mapping = {
            "suicidal_ideation": ["suicide_prevention_hotline", "emergency_services", "crisis_text_line"],
            "self_harm": ["self_harm_support", "mental_health_services", "crisis_text_line"],
            "severe_depression": ["mental_health_services", "depression_support", "therapy_referral"],
            "panic_attack": ["anxiety_support", "mental_health_services"],
            "substance_abuse": ["addiction_services", "mental_health_services"],
            "domestic_violence": ["domestic_violence_hotline", "emergency_services"],
            "eating_disorder": ["eating_disorder_support", "therapy_referral"],
            "general_distress": ["mental_health_services", "crisis_text_line"]
        }
        
        resource_categories = resource_mapping.get(crisis_type, ["mental_health_services"])
        
        # Collect resources from relevant categories
        relevant_resources = []
        for category in resource_categories:
            if category in self.resources:
                relevant_resources.extend(self.resources[category])
        
        return relevant_resources
    
    def get_emergency_resources(self) -> List[Dict[str, str]]:
        """Get emergency resources for immediate crises."""
        emergency_resources = []
        emergency_categories = ["emergency_services", "suicide_prevention_hotline", "crisis_text_line"]
        
        for category in emergency_categories:
            if category in self.resources:
                emergency_resources.extend(self.resources[category])
        
        return emergency_resources
    
    def add_resource(
        self,
        category: str,
        name: str,
        contact: str,
        description: str,
        availability: str,
        resource_type: str
    ):
        """Add a new crisis resource."""
        if category not in self.resources:
            self.resources[category] = []
        
        new_resource = {
            "name": name,
            "contact": contact,
            "description": description,
            "availability": availability,
            "type": resource_type
        }
        
        self.resources[category].append(new_resource)
        self.logger.info(f"Added new resource to {category}: {name}")
    
    def update_resource(
        self,
        category: str,
        resource_name: str,
        updates: Dict[str, str]
    ):
        """Update an existing resource."""
        if category not in self.resources:
            self.logger.warning(f"Category {category} not found")
            return
        
        for resource in self.resources[category]:
            if resource["name"] == resource_name:
                resource.update(updates)
                self.logger.info(f"Updated resource {resource_name} in {category}")
                return
        
        self.logger.warning(f"Resource {resource_name} not found in {category}")
    
    def remove_resource(self, category: str, resource_name: str):
        """Remove a resource."""
        if category not in self.resources:
            self.logger.warning(f"Category {category} not found")
            return
        
        self.resources[category] = [
            resource for resource in self.resources[category]
            if resource["name"] != resource_name
        ]
        self.logger.info(f"Removed resource {resource_name} from {category}")
    
    def get_all_resources(self) -> Dict[str, List[Dict[str, str]]]:
        """Get all available resources."""
        return self.resources.copy()
    
    def get_resource_categories(self) -> List[str]:
        """Get all resource categories."""
        return list(self.resources.keys())
    
    def search_resources(self, query: str) -> List[Dict[str, str]]:
        """Search resources by name or description."""
        query_lower = query.lower()
        matching_resources = []
        
        for category, resources in self.resources.items():
            for resource in resources:
                if (query_lower in resource["name"].lower() or 
                    query_lower in resource["description"].lower()):
                    resource_with_category = resource.copy()
                    resource_with_category["category"] = category
                    matching_resources.append(resource_with_category)
        
        return matching_resources
    
    def get_resources_by_type(self, resource_type: str) -> List[Dict[str, str]]:
        """Get resources by type (phone, text, website, etc.)."""
        matching_resources = []
        
        for category, resources in self.resources.items():
            for resource in resources:
                if resource.get("type") == resource_type:
                    resource_with_category = resource.copy()
                    resource_with_category["category"] = category
                    matching_resources.append(resource_with_category)
        
        return matching_resources
