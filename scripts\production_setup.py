#!/usr/bin/env python3
"""
Production Setup Script for AI Companion System
Prepares the system for production deployment with security checks.
"""

import os
import sys
import subprocess
import secrets
import string
from pathlib import Path

def generate_secret_key(length=32):
    """Generate a secure secret key."""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def check_environment():
    """Check if environment is properly configured."""
    print("🔍 Checking environment configuration...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found. Please copy .env.example to .env")
        return False
    
    # Check for placeholder values
    with open(env_file, 'r') as f:
        content = f.read()
        
    issues = []
    if "your_gemini_api_key_here" in content:
        issues.append("GEMINI_API_KEY not configured")
    if "dev-secret-key-change-in-production" in content:
        issues.append("SECRET_KEY still using development value")
    if "ENVIRONMENT=development" in content:
        issues.append("ENVIRONMENT still set to development")
    
    if issues:
        print("❌ Configuration issues found:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    
    print("✅ Environment configuration looks good")
    return True

def setup_production_env():
    """Setup production environment file."""
    print("🔧 Setting up production environment...")
    
    env_file = Path(".env")
    if env_file.exists():
        # Backup existing .env
        backup_file = Path(".env.backup")
        env_file.rename(backup_file)
        print(f"📦 Backed up existing .env to {backup_file}")
    
    # Copy from example
    example_file = Path(".env.example")
    if not example_file.exists():
        print("❌ .env.example not found")
        return False
    
    with open(example_file, 'r') as f:
        content = f.read()
    
    # Update for production
    content = content.replace("ENVIRONMENT=development", "ENVIRONMENT=production")
    content = content.replace("DEBUG_MODE=true", "DEBUG_MODE=false")
    content = content.replace("LOG_LEVEL=INFO", "LOG_LEVEL=WARNING")
    
    # Generate secure secret key
    secret_key = generate_secret_key()
    content = content.replace(
        "dev-secret-key-change-in-production", 
        secret_key
    )
    
    with open(env_file, 'w') as f:
        f.write(content)
    
    print("✅ Production .env file created")
    print("⚠️  Please update GEMINI_API_KEY and other required values")
    return True

def create_directories():
    """Create necessary directories."""
    print("📁 Creating necessary directories...")
    
    directories = [
        "data/db",
        "data/logs", 
        "data/cache",
        "backups",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   ✅ Created {directory}")
    
    return True

def install_dependencies():
    """Install production dependencies."""
    print("📦 Installing production dependencies...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True, capture_output=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def run_security_checks():
    """Run basic security checks."""
    print("🔒 Running security checks...")
    
    checks_passed = True
    
    # Check file permissions
    sensitive_files = [".env", "data/"]
    for file_path in sensitive_files:
        if os.path.exists(file_path):
            stat = os.stat(file_path)
            if stat.st_mode & 0o077:  # Check if group/other have permissions
                print(f"⚠️  {file_path} has overly permissive permissions")
                checks_passed = False
    
    # Check for debug mode
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r') as f:
            content = f.read()
            if "DEBUG_MODE=true" in content:
                print("⚠️  DEBUG_MODE is still enabled")
                checks_passed = False
    
    if checks_passed:
        print("✅ Security checks passed")
    
    return checks_passed

def main():
    """Main setup function."""
    print("🚀 AI Companion System - Production Setup")
    print("=" * 50)
    
    # Change to project root
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    steps = [
        ("Creating directories", create_directories),
        ("Installing dependencies", install_dependencies),
        ("Setting up production environment", setup_production_env),
        ("Running security checks", run_security_checks),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            print(f"❌ Failed: {step_name}")
            sys.exit(1)
    
    print("\n🎉 Production setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Update .env with your actual API keys and configuration")
    print("2. Review security settings")
    print("3. Set up SSL/TLS certificates")
    print("4. Configure monitoring and logging")
    print("5. Run: python test_system.py")
    print("6. Deploy using: docker-compose up -d")

if __name__ == "__main__":
    main()
