"""
Gradio Web Interface for the AI Companion System.
Provides an interactive web UI for testing and development.
"""

import asyncio
import logging
import json
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple

import gradio as gr
import pandas as pd

from ..core.conversation import ConversationService
from ..core.models import InteractionType, EmotionType, MentalHealthRisk
from ..mental_health.crisis import CrisisDetectionService
from ..config.settings import settings
from ..utils.helpers import generate_id, utc_now


class GradioInterface:
    """Gradio web interface for AI companion testing and interaction."""
    
    def __init__(
        self,
        conversation_service: ConversationService,
        crisis_detection: CrisisDetectionService
    ):
        """Initialize Gradio interface."""
        self.logger = logging.getLogger(__name__)
        self.conversation_service = conversation_service
        self.crisis_detection = crisis_detection
        
        # Session management
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.conversation_history: Dict[str, List[Dict[str, Any]]] = {}
        
        # Interface components
        self.interface = None
        self._create_interface()
        
        self.logger.info("✅ Gradio interface initialized")
    
    def _create_interface(self):
        """Create the Gradio interface."""
        with gr.Blocks(
            title="AI Companion System",
            theme=gr.themes.Soft(),
            css=self._get_custom_css()
        ) as interface:
            
            # Header
            gr.Markdown("""
            # 🤖 AI Companion System
            ### Emotional Support & Mental Health Assistant
            
            Welcome to your AI companion! I'm here to provide emotional support, listen to your concerns, 
            and help you navigate life's challenges. This is a safe space for you to express yourself.
            """)
            
            # Main chat interface
            with gr.Row():
                with gr.Column(scale=3):
                    # Chat interface
                    chatbot = gr.Chatbot(
                        label="Conversation",
                        height=500,
                        show_label=True,
                        container=True,
                        bubble_full_width=False
                    )
                    
                    with gr.Row():
                        msg_input = gr.Textbox(
                            label="Your message",
                            placeholder="Share what's on your mind...",
                            lines=3,
                            max_lines=5,
                            show_label=False,
                            container=False
                        )
                        send_btn = gr.Button("Send", variant="primary", size="lg")
                    
                    # Quick action buttons
                    with gr.Row():
                        mood_btn = gr.Button("😊 Share Mood", size="sm")
                        support_btn = gr.Button("🤗 Need Support", size="sm")
                        crisis_btn = gr.Button("🚨 Crisis Help", size="sm", variant="stop")
                
                with gr.Column(scale=1):
                    # Emotional analysis panel
                    gr.Markdown("### 📊 Emotional Analysis")
                    
                    emotion_display = gr.JSON(
                        label="Current Emotional State",
                        visible=True
                    )
                    
                    risk_level = gr.Textbox(
                        label="Risk Level",
                        interactive=False,
                        visible=True
                    )
                    
                    # User profile section
                    gr.Markdown("### 👤 User Profile")
                    
                    user_id_input = gr.Textbox(
                        label="User ID",
                        value="demo_user",
                        placeholder="Enter your user ID"
                    )
                    
                    # Memory insights
                    gr.Markdown("### 🧠 Memory Insights")
                    
                    memory_summary = gr.JSON(
                        label="Recent Memories",
                        visible=True
                    )
            
            # Advanced features
            with gr.Accordion("🔧 Advanced Features", open=False):
                with gr.Row():
                    with gr.Column():
                        gr.Markdown("### 📈 Conversation Analytics")
                        analytics_btn = gr.Button("Generate Analytics Report")
                        analytics_output = gr.JSON(label="Analytics")
                    
                    with gr.Column():
                        gr.Markdown("### 💾 Export Data")
                        export_btn = gr.Button("Export Conversation")
                        export_output = gr.File(label="Download")
            
            # Crisis resources
            with gr.Accordion("🆘 Crisis Resources", open=False):
                gr.Markdown("""
                ### Immediate Help Resources
                
                **If you're in immediate danger, please call emergency services (911) or go to your nearest emergency room.**
                
                **Crisis Hotlines:**
                - National Suicide Prevention Lifeline: **988**
                - Crisis Text Line: Text **HOME** to **741741**
                - National Domestic Violence Hotline: **1-**************
                
                **Online Resources:**
                - [Crisis Text Line](https://www.crisistextline.org/)
                - [National Suicide Prevention Lifeline](https://suicidepreventionlifeline.org/)
                - [Mental Health America](https://www.mhanational.org/)
                """)
            
            # Event handlers
            def process_message(message: str, history: List, user_id: str):
                return asyncio.run(self._handle_message_async(message, history, user_id))
            
            def quick_mood_check(history: List, user_id: str):
                return asyncio.run(self._handle_message_async(
                    "I'd like to share how I'm feeling right now.",
                    history,
                    user_id
                ))
            
            def quick_support(history: List, user_id: str):
                return asyncio.run(self._handle_message_async(
                    "I'm going through a difficult time and could use some support.",
                    history,
                    user_id
                ))
            
            def crisis_help(history: List, user_id: str):
                return asyncio.run(self._handle_crisis_async(history, user_id))
            
            def generate_analytics(user_id: str):
                return asyncio.run(self._generate_analytics_async(user_id))
            
            def export_conversation(user_id: str):
                return asyncio.run(self._export_conversation_async(user_id))
            
            # Wire up events
            send_btn.click(
                process_message,
                inputs=[msg_input, chatbot, user_id_input],
                outputs=[chatbot, emotion_display, risk_level, memory_summary]
            ).then(
                lambda: "",
                outputs=[msg_input]
            )
            
            msg_input.submit(
                process_message,
                inputs=[msg_input, chatbot, user_id_input],
                outputs=[chatbot, emotion_display, risk_level, memory_summary]
            ).then(
                lambda: "",
                outputs=[msg_input]
            )
            
            mood_btn.click(
                quick_mood_check,
                inputs=[chatbot, user_id_input],
                outputs=[chatbot, emotion_display, risk_level, memory_summary]
            )
            
            support_btn.click(
                quick_support,
                inputs=[chatbot, user_id_input],
                outputs=[chatbot, emotion_display, risk_level, memory_summary]
            )
            
            crisis_btn.click(
                crisis_help,
                inputs=[chatbot, user_id_input],
                outputs=[chatbot, emotion_display, risk_level, memory_summary]
            )
            
            analytics_btn.click(
                generate_analytics,
                inputs=[user_id_input],
                outputs=[analytics_output]
            )
            
            export_btn.click(
                export_conversation,
                inputs=[user_id_input],
                outputs=[export_output]
            )
        
        self.interface = interface
    
    async def _handle_message_async(
        self,
        message: str,
        history: List,
        user_id: str
    ) -> Tuple[List, Dict, str, Dict]:
        """Handle message processing asynchronously."""
        try:
            if not message.strip():
                return history, {}, "", {}
            
            # Process message through conversation service
            response = await self.conversation_service.process_message(
                user_id=user_id,
                message=message,
                interaction_type=InteractionType.CONVERSATION,
                context={"interface": "gradio"}
            )
            
            # Update conversation history
            if user_id not in self.conversation_history:
                self.conversation_history[user_id] = []
            
            self.conversation_history[user_id].append({
                "user": message,
                "assistant": response.get("response", "I'm sorry, I couldn't process that right now."),
                "timestamp": utc_now().isoformat(),
                "emotional_state": response.get("emotional_state", {}),
                "risk_level": response.get("risk_level", "low")
            })
            
            # Update chat history for display
            history = history or []
            history.append([message, response.get("response", "I'm sorry, I couldn't process that right now.")])
            
            # Prepare emotional analysis display
            emotional_state = response.get("emotional_state", {})
            emotion_display = {
                "primary_emotion": emotional_state.get("primary_emotion", "neutral"),
                "intensity": emotional_state.get("intensity", 0.5),
                "secondary_emotions": emotional_state.get("secondary_emotions", {}),
                "context_factors": emotional_state.get("context_factors", [])
            }
            
            # Risk level display
            risk_level = response.get("risk_level", "low").upper()
            risk_colors = {
                "LOW": "🟢 LOW",
                "MODERATE": "🟡 MODERATE", 
                "HIGH": "🟠 HIGH",
                "CRITICAL": "🔴 CRITICAL"
            }
            risk_display = risk_colors.get(risk_level, risk_level)
            
            # Memory summary
            memory_summary = response.get("memory_insights", {})
            
            return history, emotion_display, risk_display, memory_summary
            
        except Exception as e:
            self.logger.error(f"Error handling message: {e}")
            error_response = "I'm experiencing some technical difficulties. Please try again."
            history = history or []
            history.append([message, error_response])
            return history, {}, "🔴 ERROR", {}
    
    async def _handle_crisis_async(
        self,
        history: List,
        user_id: str
    ) -> Tuple[List, Dict, str, Dict]:
        """Handle crisis situation."""
        crisis_message = """I'm in crisis and need immediate help."""
        
        # Process as high-priority crisis message
        response = await self.conversation_service.process_message(
            user_id=user_id,
            message=crisis_message,
            interaction_type=InteractionType.CRISIS,
            context={"interface": "gradio", "priority": "crisis"}
        )
        
        # Add crisis resources to response
        enhanced_response = response.get("response", "") + """

🚨 **IMMEDIATE CRISIS RESOURCES:**
- **Emergency Services: 911**
- **National Suicide Prevention Lifeline: 988**
- **Crisis Text Line: Text HOME to 741741**

You are not alone. Professional help is available 24/7."""
        
        history = history or []
        history.append([crisis_message, enhanced_response])
        
        return history, {"crisis_mode": True}, "🔴 CRITICAL", {"crisis_intervention": True}
    
    async def _generate_analytics_async(self, user_id: str) -> Dict[str, Any]:
        """Generate conversation analytics."""
        try:
            if user_id not in self.conversation_history:
                return {"error": "No conversation history found"}
            
            history = self.conversation_history[user_id]
            
            # Basic analytics
            total_messages = len(history)
            emotions = [entry.get("emotional_state", {}).get("primary_emotion", "neutral") for entry in history]
            emotion_counts = pd.Series(emotions).value_counts().to_dict()
            
            risk_levels = [entry.get("risk_level", "low") for entry in history]
            risk_counts = pd.Series(risk_levels).value_counts().to_dict()
            
            # Time analysis
            timestamps = [entry.get("timestamp") for entry in history if entry.get("timestamp")]
            
            analytics = {
                "total_messages": total_messages,
                "emotion_distribution": emotion_counts,
                "risk_level_distribution": risk_counts,
                "conversation_span": {
                    "first_message": timestamps[0] if timestamps else None,
                    "last_message": timestamps[-1] if timestamps else None
                },
                "generated_at": utc_now().isoformat()
            }
            
            return analytics
            
        except Exception as e:
            self.logger.error(f"Error generating analytics: {e}")
            return {"error": str(e)}
    
    async def _export_conversation_async(self, user_id: str) -> Optional[str]:
        """Export conversation to file."""
        try:
            if user_id not in self.conversation_history:
                return None
            
            history = self.conversation_history[user_id]
            
            # Create export data
            export_data = {
                "user_id": user_id,
                "export_timestamp": utc_now().isoformat(),
                "conversation_history": history,
                "total_messages": len(history)
            }
            
            # Save to temporary file
            import tempfile
            import os
            
            with tempfile.NamedTemporaryFile(
                mode='w',
                suffix='.json',
                delete=False,
                prefix=f'ai_companion_export_{user_id}_'
            ) as f:
                json.dump(export_data, f, indent=2, default=str)
                temp_path = f.name
            
            return temp_path
            
        except Exception as e:
            self.logger.error(f"Error exporting conversation: {e}")
            return None
    
    def _get_custom_css(self) -> str:
        """Get custom CSS for the interface."""
        return """
        .gradio-container {
            max-width: 1200px !important;
        }
        
        .chat-message {
            padding: 10px;
            margin: 5px 0;
            border-radius: 10px;
        }
        
        .user-message {
            background-color: #e3f2fd;
            margin-left: 20%;
        }
        
        .assistant-message {
            background-color: #f5f5f5;
            margin-right: 20%;
        }
        
        .crisis-button {
            background-color: #d32f2f !important;
            color: white !important;
        }
        
        .emotion-display {
            background-color: #fff3e0;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        """
    
    def launch(
        self,
        share: bool = False,
        server_name: str = "0.0.0.0",
        server_port: int = None,
        debug: bool = False
    ):
        """Launch the Gradio interface."""
        try:
            port = server_port or settings.gradio_port
            
            self.logger.info(f"🚀 Launching Gradio interface on {server_name}:{port}")
            
            self.interface.launch(
                share=share,
                server_name=server_name,
                server_port=port,
                debug=debug,
                show_error=True,
                quiet=False
            )
            
        except Exception as e:
            self.logger.error(f"Error launching Gradio interface: {e}")
            raise
    
    async def close(self):
        """Close the Gradio interface."""
        try:
            if self.interface:
                self.interface.close()
            self.logger.info("Gradio interface closed")
            
        except Exception as e:
            self.logger.error(f"Error closing Gradio interface: {e}")


# Standalone function for easy testing
async def create_demo_interface():
    """Create a demo interface for testing."""
    # Import here to avoid circular dependency
    from ..main import AICompanionSystem

    # Initialize system
    system = AICompanionSystem()
    await system.initialize()

    # Create interface
    interface = GradioInterface(
        conversation_service=system.conversation_service,
        crisis_detection=system.crisis_detection
    )

    return interface


if __name__ == "__main__":
    # Demo mode
    async def main():
        interface = await create_demo_interface()
        interface.launch(share=False, debug=True)

    asyncio.run(main())
