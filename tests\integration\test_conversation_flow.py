"""
Integration tests for conversation flow in the AI Companion System.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch

from ai_companion.core.models import EmotionType, InteractionType
from ai_companion.core.conversation import ConversationService


@pytest.mark.integration
class TestConversationFlow:
    """Test complete conversation flows."""
    
    @pytest.mark.asyncio
    async def test_basic_conversation_flow(self, conversation_service, sample_user_id):
        """Test a basic conversation flow."""
        # First message
        response1 = await conversation_service.process_message(
            user_id=sample_user_id,
            message="Hello, how are you today?",
            context={"session_id": "test_session"}
        )
        
        assert response1 is not None
        assert "response" in response1
        assert "emotion_detected" in response1
        assert "conversation_id" in response1
        
        # Follow-up message
        response2 = await conversation_service.process_message(
            user_id=sample_user_id,
            message="I'm feeling a bit stressed about work",
            context={"session_id": "test_session"}
        )
        
        assert response2 is not None
        assert response2["emotion_detected"] in ["stress", "anxiety"]
        assert response2["conversation_id"] == response1["conversation_id"]
    
    @pytest.mark.asyncio
    async def test_emotional_conversation_flow(self, conversation_service, sample_user_id):
        """Test conversation flow with emotional content."""
        messages = [
            ("I'm feeling really anxious about my presentation tomorrow", EmotionType.ANXIETY),
            ("I can't sleep because I keep thinking about it", EmotionType.ANXIETY),
            ("What if I mess up in front of everyone?", EmotionType.FEAR),
            ("Thank you, that actually helps a lot", EmotionType.GRATITUDE)
        ]
        
        conversation_id = None
        
        for message, expected_emotion in messages:
            response = await conversation_service.process_message(
                user_id=sample_user_id,
                message=message,
                context={"session_id": "emotional_test"}
            )
            
            assert response is not None
            assert "response" in response
            
            if conversation_id is None:
                conversation_id = response["conversation_id"]
            else:
                assert response["conversation_id"] == conversation_id
            
            # Check that emotion is detected (allowing for some flexibility)
            detected_emotion = response.get("emotion_detected")
            assert detected_emotion is not None
    
    @pytest.mark.asyncio
    async def test_memory_integration_in_conversation(self, conversation_service, sample_user_id):
        """Test that memories are created and retrieved during conversation."""
        # First conversation about work stress
        await conversation_service.process_message(
            user_id=sample_user_id,
            message="I'm really stressed about my upcoming work presentation",
            context={"session_id": "memory_test"}
        )
        
        # Wait a bit to ensure memory is stored
        await asyncio.sleep(0.1)
        
        # Second conversation referencing work
        response = await conversation_service.process_message(
            user_id=sample_user_id,
            message="How can I prepare better for work presentations?",
            context={"session_id": "memory_test_2"}
        )
        
        assert response is not None
        # The AI should have context from the previous conversation
        # This is tested by checking if the response is contextually appropriate
        assert "response" in response
    
    @pytest.mark.asyncio
    async def test_crisis_detection_in_conversation(self, conversation_service, sample_user_id):
        """Test crisis detection during conversation."""
        crisis_message = "I can't handle this anymore, I don't want to be here"
        
        response = await conversation_service.process_message(
            user_id=sample_user_id,
            message=crisis_message,
            context={"session_id": "crisis_test"}
        )
        
        assert response is not None
        assert "risk_level" in response
        assert response["risk_level"] in ["moderate", "high", "critical"]
        
        # Crisis response should be supportive and immediate
        assert "response" in response
        crisis_response = response["response"].lower()
        assert any(word in crisis_response for word in ["support", "help", "here", "care"])
    
    @pytest.mark.asyncio
    async def test_conversation_context_persistence(self, conversation_service, sample_user_id):
        """Test that conversation context persists across messages."""
        session_id = "context_test"
        
        # Start conversation about a specific topic
        response1 = await conversation_service.process_message(
            user_id=sample_user_id,
            message="I'm having trouble with my relationship",
            context={"session_id": session_id}
        )
        
        conversation_id = response1["conversation_id"]
        
        # Continue conversation with pronoun reference
        response2 = await conversation_service.process_message(
            user_id=sample_user_id,
            message="We've been arguing a lot lately",
            context={"session_id": session_id}
        )
        
        # Should maintain same conversation
        assert response2["conversation_id"] == conversation_id
        
        # Follow up with more context
        response3 = await conversation_service.process_message(
            user_id=sample_user_id,
            message="What should I do about it?",
            context={"session_id": session_id}
        )
        
        assert response3["conversation_id"] == conversation_id
        # Response should be contextually appropriate for relationship advice
        assert "response" in response3
    
    @pytest.mark.asyncio
    async def test_multiple_users_isolation(self, conversation_service):
        """Test that conversations between different users are isolated."""
        user1_id = "user1"
        user2_id = "user2"
        
        # User 1 shares personal information
        response1 = await conversation_service.process_message(
            user_id=user1_id,
            message="I'm dealing with anxiety about my job interview",
            context={"session_id": "user1_session"}
        )
        
        # User 2 asks about anxiety
        response2 = await conversation_service.process_message(
            user_id=user2_id,
            message="Can you help me with anxiety?",
            context={"session_id": "user2_session"}
        )
        
        # Responses should be different conversation IDs
        assert response1["conversation_id"] != response2["conversation_id"]
        
        # User 2 should not get information about User 1's job interview
        user2_response = response2["response"].lower()
        assert "job interview" not in user2_response
    
    @pytest.mark.asyncio
    async def test_conversation_performance(self, conversation_service, sample_user_id, performance_thresholds):
        """Test conversation response time performance."""
        import time
        
        message = "How are you feeling today?"
        
        start_time = time.time()
        response = await conversation_service.process_message(
            user_id=sample_user_id,
            message=message,
            context={"session_id": "performance_test"}
        )
        end_time = time.time()
        
        response_time = end_time - start_time
        
        assert response is not None
        assert response_time < performance_thresholds["max_response_time"]
        
        # Check if response time is included in the response
        if "response_time" in response:
            assert response["response_time"] < performance_thresholds["max_response_time"]


@pytest.mark.integration
@pytest.mark.slow
class TestLongConversationFlow:
    """Test longer conversation flows."""
    
    @pytest.mark.asyncio
    async def test_extended_conversation(self, conversation_service, sample_user_id):
        """Test an extended conversation with multiple topics."""
        messages = [
            "Hi, I'm having a difficult day",
            "I'm feeling overwhelmed with work and personal life",
            "My boss gave me a huge project with an impossible deadline",
            "And my partner and I had a fight last night",
            "I don't know how to handle all of this stress",
            "Do you have any suggestions for managing stress?",
            "That's helpful, thank you",
            "I think I'll try some of those techniques",
            "Actually, I'm feeling a bit better just talking about it"
        ]
        
        conversation_id = None
        session_id = "extended_test"
        
        for i, message in enumerate(messages):
            response = await conversation_service.process_message(
                user_id=sample_user_id,
                message=message,
                context={"session_id": session_id, "message_number": i + 1}
            )
            
            assert response is not None
            assert "response" in response
            
            if conversation_id is None:
                conversation_id = response["conversation_id"]
            else:
                # Should maintain same conversation throughout
                assert response["conversation_id"] == conversation_id
            
            # Add small delay to simulate real conversation
            await asyncio.sleep(0.05)
        
        # The conversation should show emotional progression
        # This is a basic check - in a real system you'd want more sophisticated analysis
        assert conversation_id is not None


@pytest.mark.integration
@pytest.mark.requires_redis
class TestConversationWithCaching:
    """Test conversation flows with caching enabled."""
    
    @pytest.mark.asyncio
    async def test_conversation_with_cache_hits(self, conversation_service, sample_user_id):
        """Test that repeated similar messages benefit from caching."""
        message = "How can I reduce stress?"
        
        # First request
        response1 = await conversation_service.process_message(
            user_id=sample_user_id,
            message=message,
            context={"session_id": "cache_test_1"}
        )
        
        # Second request with same message (different session to avoid conversation context)
        response2 = await conversation_service.process_message(
            user_id=sample_user_id,
            message=message,
            context={"session_id": "cache_test_2"}
        )
        
        assert response1 is not None
        assert response2 is not None
        
        # Responses might be similar due to caching, but should still be valid
        assert "response" in response1
        assert "response" in response2
