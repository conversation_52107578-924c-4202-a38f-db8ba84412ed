# AI Companion System - Development Dockerfile
# Optimized for development with hot reloading and debugging capabilities

FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app/src

# Set work directory
WORKDIR /app

# Install system dependencies including development tools
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    git \
    vim \
    htop \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for development
RUN useradd --create-home --shell /bin/bash --uid 1000 dev

# Copy requirements first for better caching
COPY requirements.txt requirements-dev.txt ./

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r requirements-dev.txt

# Install additional development tools
RUN pip install --no-cache-dir \
    debugpy \
    jupyter \
    jupyterlab \
    ipython

# Copy application code
COPY src/ src/
COPY tests/ tests/
COPY scripts/ scripts/
COPY .env.example .env

# Create necessary directories and set permissions
RUN mkdir -p data/logs data/db data/cache notebooks && \
    chown -R dev:dev /app

# Switch to non-root user
USER dev

# Create Jupyter config
RUN jupyter lab --generate-config && \
    echo "c.ServerApp.ip = '0.0.0.0'" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.allow_root = True" >> ~/.jupyter/jupyter_lab_config.py && \
    echo "c.ServerApp.token = ''" >> ~/.jupyter/jupyter_lab_config.py

# Expose ports
EXPOSE 7860 8000 8888 5678

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Default command for development
CMD ["python", "-m", "ai_companion.main"]
