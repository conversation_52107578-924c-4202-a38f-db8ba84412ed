"""
Data models for Mental Health Analytics.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Any


@dataclass
class AnalyticsInsight:
    """Data class for analytics insights."""
    insight_id: str
    title: str
    description: str
    data: Dict[str, Any]
    confidence: float
    timestamp: datetime
    category: str


@dataclass
class UserAnalytics:
    """User-specific analytics data."""
    user_id: str
    emotional_patterns: Dict[str, Any]
    interaction_patterns: Dict[str, Any]
    progress_trends: Dict[str, Any]
    engagement_score: float
    consistency_score: float
    recommendations: list[str]
    timestamp: datetime


@dataclass
class PopulationAnalytics:
    """Population-level analytics data."""
    total_users: int
    emotion_trends: Dict[str, Any]
    risk_patterns: Dict[str, Any]
    intervention_effectiveness: Dict[str, Any]
    recommendations: list[str]
    timestamp: datetime
