"""
Pytest configuration and fixtures for the AI Companion System tests.
"""

import pytest
import asyncio
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, AsyncMock

# Add src to path for imports
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from ai_companion.config.settings import Settings
from ai_companion.core.models import EmotionType, InteractionType, MemoryType
from ai_companion.services.gemini import GeminiService
from ai_companion.services.storage import StorageService
from ai_companion.core.memory import MemoryService
from ai_companion.core.emotions import EmotionalIntelligenceService
from ai_companion.core.conversation import ConversationService


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir():
    """Create a temporary directory for test data."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def test_settings(temp_dir):
    """Create test settings with temporary database."""
    return Settings(
        gemini_api_key="test_api_key",
        database_url=f"sqlite:///{temp_dir}/test.db",
        redis_url="redis://localhost:6379/1",  # Use test database
        environment="testing",
        debug_mode=True,
        log_level="DEBUG"
    )


@pytest.fixture
def mock_gemini_service():
    """Create a mock Gemini service for testing."""
    service = Mock(spec=GeminiService)
    service.generate_response = AsyncMock(return_value="Test response from AI")
    service.analyze_emotion = AsyncMock(return_value={
        "primary_emotion": "neutral",
        "confidence": 0.8,
        "intensity": 0.5
    })
    return service


@pytest.fixture
async def storage_service(test_settings, temp_dir):
    """Create a test storage service."""
    service = StorageService(settings=test_settings)
    await service.initialize()
    yield service
    await service.close()


@pytest.fixture
async def memory_service(storage_service):
    """Create a test memory service."""
    service = MemoryService(storage_service)
    yield service


@pytest.fixture
async def emotional_intelligence_service(mock_gemini_service):
    """Create a test emotional intelligence service."""
    service = EmotionalIntelligenceService(mock_gemini_service)
    yield service


@pytest.fixture
async def conversation_service(memory_service, emotional_intelligence_service, mock_gemini_service):
    """Create a test conversation service."""
    service = ConversationService(
        memory_service=memory_service,
        emotional_intelligence=emotional_intelligence_service,
        gemini_service=mock_gemini_service
    )
    yield service


@pytest.fixture
def sample_user_id():
    """Sample user ID for testing."""
    return "test_user_123"


@pytest.fixture
def sample_message():
    """Sample message for testing."""
    return "I'm feeling anxious about my upcoming presentation at work."


@pytest.fixture
def sample_memory_entry():
    """Sample memory entry for testing."""
    return {
        "user_id": "test_user_123",
        "content": "User mentioned feeling anxious about work presentation",
        "interaction_type": InteractionType.EMOTION,
        "emotion": EmotionType.ANXIETY,
        "importance": 0.8
    }


@pytest.fixture
def sample_emotional_state():
    """Sample emotional state for testing."""
    return {
        "primary_emotion": EmotionType.ANXIETY,
        "intensity": 0.7,
        "confidence": 0.85,
        "valence": -0.3,
        "arousal": 0.6
    }


# Test data fixtures
@pytest.fixture
def crisis_messages():
    """Sample crisis messages for testing."""
    return [
        "I can't handle this anymore",
        "I don't want to be here",
        "Nothing matters anymore",
        "I'm thinking about ending it all"
    ]


@pytest.fixture
def normal_messages():
    """Sample normal messages for testing."""
    return [
        "How are you today?",
        "I had a good day at work",
        "I'm excited about the weekend",
        "Can you help me with something?"
    ]


@pytest.fixture
def emotional_messages():
    """Sample emotional messages for testing."""
    return {
        EmotionType.JOY: "I'm so happy about my promotion!",
        EmotionType.SADNESS: "I'm feeling really down today",
        EmotionType.ANGER: "I'm frustrated with my coworker",
        EmotionType.ANXIETY: "I'm worried about the test tomorrow",
        EmotionType.FEAR: "I'm scared about the medical results"
    }


# Mock external services
@pytest.fixture
def mock_redis():
    """Mock Redis client for testing."""
    redis_mock = Mock()
    redis_mock.get = Mock(return_value=None)
    redis_mock.set = Mock(return_value=True)
    redis_mock.delete = Mock(return_value=1)
    redis_mock.exists = Mock(return_value=False)
    redis_mock.keys = Mock(return_value=[])
    return redis_mock


@pytest.fixture
def mock_database():
    """Mock database session for testing."""
    db_mock = Mock()
    db_mock.add = Mock()
    db_mock.commit = Mock()
    db_mock.rollback = Mock()
    db_mock.query = Mock()
    return db_mock


# Performance testing fixtures
@pytest.fixture
def performance_thresholds():
    """Performance thresholds for testing."""
    return {
        "max_response_time": 1.0,  # 1 second max for tests
        "min_cache_hit_rate": 0.5,  # 50% minimum for tests
        "max_memory_usage": 100,    # 100MB max for tests
    }


# Async test helpers
@pytest.fixture
def async_test_timeout():
    """Timeout for async tests."""
    return 10.0  # 10 seconds


# Cleanup fixtures
@pytest.fixture(autouse=True)
def cleanup_test_data():
    """Automatically cleanup test data after each test."""
    yield
    # Cleanup code here if needed
    pass


# Markers for different test types
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "performance: Performance tests")
    config.addinivalue_line("markers", "slow: Slow tests")
    config.addinivalue_line("markers", "requires_redis: Tests that require Redis")
    config.addinivalue_line("markers", "requires_api_key: Tests that require API key")
