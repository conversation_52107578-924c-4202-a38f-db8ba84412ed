"""
Population-Level Analytics for Mental Health Analysis.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from collections import defaultdict, Counter

from ...core.models import EmotionType, MentalHealthRisk, utc_now
from ...services.storage import StorageService
from ..privacy import DataAnonymizer
from .models import PopulationAnalytics
from ...config.settings import settings


class PopulationAnalyzer:
    """Analyzer for population-level mental health patterns."""
    
    def __init__(self, storage_service: StorageService):
        """Initialize population analyzer."""
        self.logger = logging.getLogger(__name__)
        self.storage_service = storage_service
        self.anonymizer = DataAnonymizer()
        self.min_cohort_size = settings.min_cohort_size
    
    async def analyze_population(
        self,
        start_date: datetime,
        end_date: datetime,
        filters: Optional[Dict[str, Any]] = None
    ) -> PopulationAnalytics:
        """Analyze population-level patterns with privacy protection."""
        try:
            # Get aggregated data
            aggregated_data = await self._get_aggregated_data(start_date, end_date, filters)
            
            if aggregated_data["total_users"] < self.min_cohort_size:
                return self._create_insufficient_data_analytics()
            
            # Analyze different aspects
            emotion_trends = self._analyze_emotion_trends(aggregated_data)
            risk_patterns = self._analyze_risk_patterns(aggregated_data)
            intervention_effectiveness = self._analyze_intervention_effectiveness(aggregated_data)
            
            # Generate recommendations
            recommendations = self._generate_population_recommendations(
                emotion_trends, risk_patterns, intervention_effectiveness
            )
            
            return PopulationAnalytics(
                total_users=aggregated_data["total_users"],
                emotion_trends=emotion_trends,
                risk_patterns=risk_patterns,
                intervention_effectiveness=intervention_effectiveness,
                recommendations=recommendations,
                timestamp=utc_now()
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing population: {e}")
            return self._create_error_analytics(str(e))
    
    async def _get_aggregated_data(
        self,
        start_date: datetime,
        end_date: datetime,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Get aggregated, anonymized data for analysis."""
        try:
            # Get anonymized user data
            user_data = await self.storage_service.get_anonymized_user_data(
                start_date, end_date, filters
            )
            
            # Aggregate data while preserving privacy
            aggregated = {
                "total_users": len(user_data),
                "emotion_distribution": defaultdict(int),
                "risk_distribution": defaultdict(int),
                "interaction_counts": defaultdict(int),
                "temporal_patterns": defaultdict(list)
            }
            
            for user in user_data:
                # Aggregate emotions
                for emotion in user.get("emotions", []):
                    aggregated["emotion_distribution"][emotion] += 1
                
                # Aggregate risk levels
                risk_level = user.get("risk_level", "low")
                aggregated["risk_distribution"][risk_level] += 1
                
                # Aggregate interactions
                interaction_count = user.get("interaction_count", 0)
                aggregated["interaction_counts"]["total"] += interaction_count
            
            return aggregated
            
        except Exception as e:
            self.logger.error(f"Error getting aggregated data: {e}")
            return {"total_users": 0}
    
    def _analyze_emotion_trends(self, aggregated_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze emotion trends across population."""
        try:
            emotion_dist = aggregated_data.get("emotion_distribution", {})
            total_emotions = sum(emotion_dist.values())
            
            if total_emotions == 0:
                return {"error": "No emotion data available"}
            
            # Calculate percentages
            emotion_percentages = {
                emotion: (count / total_emotions) * 100
                for emotion, count in emotion_dist.items()
            }
            
            # Identify concerning trends
            concerning_emotions = ["sadness", "anxiety", "anger", "fear"]
            concerning_percentage = sum(
                emotion_percentages.get(emotion, 0)
                for emotion in concerning_emotions
            )
            
            # Positive emotions
            positive_emotions = ["joy", "excitement", "hope", "love"]
            positive_percentage = sum(
                emotion_percentages.get(emotion, 0)
                for emotion in positive_emotions
            )
            
            return {
                "emotion_distribution": emotion_percentages,
                "concerning_emotions_percentage": concerning_percentage,
                "positive_emotions_percentage": positive_percentage,
                "most_common_emotions": sorted(
                    emotion_percentages.items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:5],
                "total_emotional_events": total_emotions
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing emotion trends: {e}")
            return {"error": str(e)}
    
    def _analyze_risk_patterns(self, aggregated_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze risk patterns across population."""
        try:
            risk_dist = aggregated_data.get("risk_distribution", {})
            total_users = aggregated_data.get("total_users", 0)
            
            if total_users == 0:
                return {"error": "No user data available"}
            
            # Calculate risk percentages
            risk_percentages = {
                risk: (count / total_users) * 100
                for risk, count in risk_dist.items()
            }
            
            # Calculate high-risk percentage
            high_risk_count = risk_dist.get("high", 0) + risk_dist.get("critical", 0)
            high_risk_percentage = (high_risk_count / total_users) * 100
            
            return {
                "risk_distribution": risk_percentages,
                "high_risk_percentage": high_risk_percentage,
                "total_users_analyzed": total_users,
                "risk_categories": {
                    "low": risk_percentages.get("low", 0),
                    "moderate": risk_percentages.get("moderate", 0),
                    "high": risk_percentages.get("high", 0),
                    "critical": risk_percentages.get("critical", 0)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing risk patterns: {e}")
            return {"error": str(e)}
    
    def _analyze_intervention_effectiveness(self, aggregated_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze effectiveness of interventions."""
        try:
            # This would require more sophisticated tracking of interventions
            # For now, return placeholder data
            return {
                "interventions_triggered": 0,
                "follow_up_engagement": 0.0,
                "risk_reduction_rate": 0.0,
                "user_satisfaction": 0.0,
                "note": "Intervention effectiveness tracking requires more data collection"
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing intervention effectiveness: {e}")
            return {"error": str(e)}
    
    def _generate_population_recommendations(
        self,
        emotion_trends: Dict[str, Any],
        risk_patterns: Dict[str, Any],
        intervention_effectiveness: Dict[str, Any]
    ) -> List[str]:
        """Generate population-level recommendations."""
        recommendations = []
        
        # Emotion-based recommendations
        if "concerning_emotions_percentage" in emotion_trends:
            concerning_pct = emotion_trends["concerning_emotions_percentage"]
            if concerning_pct > 40:
                recommendations.append(
                    "High levels of concerning emotions detected - consider enhanced support resources"
                )
            elif concerning_pct > 25:
                recommendations.append(
                    "Moderate levels of concerning emotions - monitor trends closely"
                )
        
        # Risk-based recommendations
        if "high_risk_percentage" in risk_patterns:
            high_risk_pct = risk_patterns["high_risk_percentage"]
            if high_risk_pct > 10:
                recommendations.append(
                    "Elevated high-risk users - prioritize crisis intervention resources"
                )
            elif high_risk_pct > 5:
                recommendations.append(
                    "Monitor high-risk users closely and ensure intervention protocols are active"
                )
        
        # General recommendations
        if not recommendations:
            recommendations.append("Population mental health indicators are within normal ranges")
        
        recommendations.append("Continue monitoring trends and adjust support strategies as needed")
        
        return recommendations
    
    def _create_insufficient_data_analytics(self) -> PopulationAnalytics:
        """Create analytics for insufficient data scenarios."""
        return PopulationAnalytics(
            total_users=0,
            emotion_trends={"error": "Insufficient data for analysis (privacy protection)"},
            risk_patterns={"error": "Insufficient data for analysis (privacy protection)"},
            intervention_effectiveness={"error": "Insufficient data for analysis"},
            recommendations=[
                f"Need at least {self.min_cohort_size} users for population analysis",
                "Continue collecting data while maintaining privacy standards"
            ],
            timestamp=utc_now()
        )
    
    def _create_error_analytics(self, error_message: str) -> PopulationAnalytics:
        """Create analytics for error scenarios."""
        return PopulationAnalytics(
            total_users=0,
            emotion_trends={"error": error_message},
            risk_patterns={"error": error_message},
            intervention_effectiveness={"error": error_message},
            recommendations=["Unable to generate recommendations due to analysis error"],
            timestamp=utc_now()
        )
