#!/usr/bin/env python3
"""
Health Check Script for AI Companion System
Validates system configuration and dependencies before deployment.
"""

import os
import sys
import asyncio
import subprocess
from pathlib import Path
from typing import List, Dict, Any

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def print_header():
    """Print health check header."""
    print("=" * 60)
    print("🏥 AI Companion System - Health Check")
    print("=" * 60)
    print()

def check_python_version() -> bool:
    """Check Python version compatibility."""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 9):
        print(f"❌ Python 3.9+ required, found {sys.version_info.major}.{sys.version_info.minor}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_environment_file() -> bool:
    """Check if .env file exists and has required variables."""
    print("📄 Checking environment configuration...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found")
        print("💡 Copy .env.example to .env and configure your settings")
        return False
    
    # Check for required variables
    required_vars = ["GEMINI_API_KEY"]
    missing_vars = []
    
    try:
        with open(env_file, 'r') as f:
            content = f.read()
        
        for var in required_vars:
            if f"{var}=" not in content:
                missing_vars.append(var)
            elif f"{var}=your_" in content or f"{var}=change_" in content:
                missing_vars.append(f"{var} (placeholder value)")
        
        if missing_vars:
            print(f"❌ Missing or placeholder values: {', '.join(missing_vars)}")
            return False
        
        print("✅ Environment file configured")
        return True
        
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False

def check_dependencies() -> bool:
    """Check if required dependencies are installed."""
    print("📦 Checking dependencies...")
    
    required_packages = [
        "google-generativeai",
        "fastapi",
        "gradio",
        "sqlalchemy",
        "pydantic",
        "python-dotenv"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("💡 Run: pip install -r requirements.txt")
        return False
    
    print("✅ All required dependencies installed")
    return True

def check_data_directories() -> bool:
    """Check if data directories exist."""
    print("📁 Checking data directories...")
    
    required_dirs = ["data", "data/db", "data/logs", "data/cache"]
    missing_dirs = []
    
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print(f"❌ Missing directories: {', '.join(missing_dirs)}")
        print("💡 Run: make setup")
        return False
    
    print("✅ Data directories exist")
    return True

async def check_system_services() -> bool:
    """Check if system services can be initialized."""
    print("🔧 Checking system services...")
    
    try:
        from ai_companion.config.settings import settings, validate_settings
        
        # Validate settings
        validate_settings()
        print("✅ Settings validation passed")
        
        # Test basic imports
        from ai_companion.services.gemini import GeminiService
        from ai_companion.services.storage import StorageService
        from ai_companion.core.memory import MemoryService
        
        print("✅ Core services can be imported")
        
        # Test Gemini service initialization
        try:
            gemini_service = GeminiService()
            print("✅ Gemini service initialized")
        except Exception as e:
            if "api key" in str(e).lower():
                print("❌ Gemini API key issue")
                return False
            else:
                print(f"⚠️ Gemini service warning: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Service check failed: {e}")
        return False

def check_ports() -> bool:
    """Check if required ports are available."""
    print("🌐 Checking port availability...")
    
    import socket
    
    ports_to_check = [7860, 8000]  # Gradio and API ports
    busy_ports = []
    
    for port in ports_to_check:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        
        if result == 0:
            busy_ports.append(port)
    
    if busy_ports:
        print(f"⚠️ Ports in use: {', '.join(map(str, busy_ports))}")
        print("💡 These ports may need to be freed or configure different ports")
    else:
        print("✅ Required ports available")
    
    return True

def print_summary(checks: Dict[str, bool]):
    """Print health check summary."""
    print()
    print("=" * 60)
    print("📊 HEALTH CHECK SUMMARY")
    print("=" * 60)
    
    passed = sum(checks.values())
    total = len(checks)
    
    for check_name, result in checks.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{check_name:<30} {status}")
    
    print()
    print(f"Overall: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 System is healthy and ready to run!")
        return True
    else:
        print("⚠️ System has issues that need to be addressed")
        return False

async def main():
    """Main health check function."""
    print_header()
    
    checks = {
        "Python Version": check_python_version(),
        "Environment File": check_environment_file(),
        "Dependencies": check_dependencies(),
        "Data Directories": check_data_directories(),
        "System Services": await check_system_services(),
        "Port Availability": check_ports()
    }
    
    system_healthy = print_summary(checks)
    
    if not system_healthy:
        print("\n💡 To fix issues:")
        print("1. Run: make setup")
        print("2. Edit .env file with your API keys")
        print("3. Install dependencies: pip install -r requirements.txt")
        print("4. Run health check again: python scripts/health_check.py")
        sys.exit(1)
    else:
        print("\n🚀 Ready to start the AI Companion System!")
        print("Run: python -m ai_companion.main")

if __name__ == "__main__":
    asyncio.run(main())
