#!/usr/bin/env python3
"""
Cleanup script to remove redundant and old files from the AI Companion System.
This script helps clean up the codebase after restructuring.
"""

import os
import shutil
from pathlib import Path


def print_banner():
    """Print cleanup banner."""
    print("=" * 60)
    print("🧹 AI Companion System - Cleanup Script")
    print("=" * 60)
    print()


def remove_files(file_list, description):
    """Remove a list of files."""
    print(f"🗑️  Removing {description}...")
    
    removed_count = 0
    for file_path in file_list:
        path = Path(file_path)
        if path.exists():
            try:
                if path.is_file():
                    path.unlink()
                elif path.is_dir():
                    shutil.rmtree(path)
                print(f"   ✅ Removed: {file_path}")
                removed_count += 1
            except Exception as e:
                print(f"   ❌ Failed to remove {file_path}: {e}")
        else:
            print(f"   ⚠️  Not found: {file_path}")
    
    print(f"   Removed {removed_count} items")
    print()


def main():
    """Main cleanup function."""
    print_banner()
    
    # Redundant main files
    redundant_main_files = [
        "enhanced_main.py",
        "next_gen_main.py", 
        "optimized_main.py",
        "launch_next_gen.py",
        "launch_optimized_demo.py"
    ]
    
    # Redundant service files
    redundant_service_files = [
        "next_gen_conversation_service.py",
        "optimized_conversation_service.py",
        "ultra_fast_conversation_service.py",
        "optimized_memory_service.py",
        "enhanced_emotional_intelligence.py",
        "advanced_emotional_intelligence.py",
        "next_gen_emotional_intelligence.py"
    ]
    
    # Redundant interface files
    redundant_interface_files = [
        "optimized_gradio_interface.py",
        "gradio_with_login.py",
        "simple_login_gradio.py"
    ]
    
    # Redundant test files (keeping only organized ones)
    redundant_test_files = [
        "simple_test.py",
        "simple_demo.py",
        "simple_gemini_test.py",
        "simple_local_test.py",
        "simple_next_gen_test.py",
        "simple_performance_test.py",
        "test_complete_enhanced_system.py",
        "test_conversation_flow.py",
        "test_emotional_ai.py",
        "test_enhanced_system.py",
        "test_fixes_verification.py",
        "test_gemini_models.py",
        "test_next_gen_system.py",
        "test_optimized_system.py",
        "test_real_api.py",
        "test_real_gemini_optimized.py",
        "test_system.py",
        "test_ultra_fast_system.py"
    ]
    
    # Redundant architecture files
    redundant_architecture_files = [
        "enhanced_memory_architecture.py",
        "ultra_performance_system.py",
        "enhanced_mental_health_platform.py",
        "mental_health_data_platform.py",
        "mental_health_platform.py",
        "production_whatsapp_bot.py",
        "whatsapp_bot_integration.py",
        "user_memory_manager.py"
    ]
    
    # Redundant documentation files
    redundant_docs = [
        "DEPLOYMENT_GUIDE.md",
        "ENHANCED_DEPLOYMENT_GUIDE.md", 
        "ENHANCEMENT_SUMMARY.md",
        "FAST_EFFICIENT_SYSTEM_GUIDE.md",
        "NEXT_GEN_README.md",
        "PERFORMANCE_OPTIMIZATION_SUMMARY.md",
        "SYSTEM_FIXES_SUMMARY.md"
    ]
    
    # Redundant utility files
    redundant_utils = [
        "microservices_config.py",
        "monitoring_service.py",
        "performance_benchmark.py",
        "deploy.py",
        "demo_app.py"
    ]
    
    # Cache and temporary files
    cache_files = [
        "__pycache__",
        "*.pyc",
        "*.pyo",
        "*.pyd",
        ".pytest_cache",
        ".coverage",
        "htmlcov",
        ".mypy_cache"
    ]
    
    # Database and log files (optional cleanup)
    data_files = [
        "ai_companion.db",
        "users.db",
        "ai_companion_performance.log"
    ]
    
    # Perform cleanup
    remove_files(redundant_main_files, "redundant main files")
    remove_files(redundant_service_files, "redundant service files")
    remove_files(redundant_interface_files, "redundant interface files")
    remove_files(redundant_test_files, "redundant test files")
    remove_files(redundant_architecture_files, "redundant architecture files")
    remove_files(redundant_docs, "redundant documentation files")
    remove_files(redundant_utils, "redundant utility files")
    
    # Clean cache files
    print("🧹 Cleaning cache files...")
    cache_removed = 0
    
    # Remove __pycache__ directories
    for pycache_dir in Path(".").rglob("__pycache__"):
        if pycache_dir.is_dir():
            try:
                shutil.rmtree(pycache_dir)
                print(f"   ✅ Removed: {pycache_dir}")
                cache_removed += 1
            except Exception as e:
                print(f"   ❌ Failed to remove {pycache_dir}: {e}")
    
    # Remove .pyc files
    for pyc_file in Path(".").rglob("*.pyc"):
        try:
            pyc_file.unlink()
            print(f"   ✅ Removed: {pyc_file}")
            cache_removed += 1
        except Exception as e:
            print(f"   ❌ Failed to remove {pyc_file}: {e}")
    
    print(f"   Removed {cache_removed} cache items")
    print()
    
    # Optional: Clean data files (ask user)
    print("🗃️  Data files cleanup (optional):")
    print("   The following files contain data that might be useful:")
    for data_file in data_files:
        if Path(data_file).exists():
            print(f"   - {data_file}")
    
    response = input("   Remove data files? (y/N): ").strip().lower()
    if response in ['y', 'yes']:
        remove_files(data_files, "data files")
    else:
        print("   Keeping data files")
        print()
    
    print("✨ Cleanup completed!")
    print()
    print("Remaining structure:")
    print("├── src/ai_companion/     # Clean, organized source code")
    print("├── tests/               # Organized test suite")
    print("├── docs/                # Documentation")
    print("├── scripts/             # Utility scripts")
    print("├── data/                # Data directory")
    print("├── requirements.txt     # Clean dependencies")
    print("├── pyproject.toml       # Modern Python configuration")
    print("├── README.md            # Comprehensive documentation")
    print("└── .env.example         # Environment template")
    print()


if __name__ == "__main__":
    main()
