# AI Companion System - Docker Compose Configuration

version: '3.8'

services:
  ai-companion:
    build: .
    ports:
      - "7860:7860"  # Gradio interface
      - "8000:8000"  # FastAPI
    environment:
      - ENVIRONMENT=production
      - DEBUG_MODE=false
      - DATABASE_URL=sqlite:///./data/db/ai_companion.db
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=INFO
    volumes:
      - ./data:/app/data
      - ./.env:/app/.env
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Optional: PostgreSQL for production
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ai_companion
      POSTGRES_USER: ai_companion
      POSTGRES_PASSWORD: change_this_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai_companion"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles:
      - postgres

  # Optional: Nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - ai-companion
    restart: unless-stopped
    profiles:
      - nginx

volumes:
  redis_data:
  postgres_data:
