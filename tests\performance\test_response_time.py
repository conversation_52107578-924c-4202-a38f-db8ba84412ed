"""
Performance tests for the AI Companion System.
"""

import pytest
import asyncio
import time
import statistics
from concurrent.futures import ThreadPoolExecutor

from ai_companion.core.conversation import ConversationService


@pytest.mark.performance
class TestResponseTimePerformance:
    """Test response time performance."""
    
    @pytest.mark.asyncio
    async def test_single_message_response_time(self, conversation_service, sample_user_id, performance_thresholds):
        """Test response time for a single message."""
        message = "How are you today?"
        
        start_time = time.time()
        response = await conversation_service.process_message(
            user_id=sample_user_id,
            message=message,
            context={"session_id": "perf_test_single"}
        )
        end_time = time.time()
        
        response_time = end_time - start_time
        
        assert response is not None
        assert response_time < performance_thresholds["max_response_time"]
        print(f"Single message response time: {response_time:.3f}s")
    
    @pytest.mark.asyncio
    async def test_multiple_messages_response_time(self, conversation_service, sample_user_id, performance_thresholds):
        """Test response time for multiple sequential messages."""
        messages = [
            "Hello, how are you?",
            "I'm feeling stressed about work",
            "Can you help me with anxiety?",
            "What are some coping strategies?",
            "Thank you for your help"
        ]
        
        response_times = []
        session_id = "perf_test_multiple"
        
        for i, message in enumerate(messages):
            start_time = time.time()
            response = await conversation_service.process_message(
                user_id=sample_user_id,
                message=message,
                context={"session_id": session_id, "message_number": i}
            )
            end_time = time.time()
            
            response_time = end_time - start_time
            response_times.append(response_time)
            
            assert response is not None
            assert response_time < performance_thresholds["max_response_time"]
        
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        
        print(f"Average response time: {avg_response_time:.3f}s")
        print(f"Max response time: {max_response_time:.3f}s")
        
        assert avg_response_time < performance_thresholds["max_response_time"]
    
    @pytest.mark.asyncio
    async def test_concurrent_users_response_time(self, conversation_service, performance_thresholds):
        """Test response time with concurrent users."""
        num_users = 5
        messages_per_user = 3
        
        async def user_conversation(user_id: str):
            """Simulate a user conversation."""
            response_times = []
            session_id = f"perf_test_concurrent_{user_id}"
            
            for i in range(messages_per_user):
                message = f"Message {i+1} from {user_id}"
                
                start_time = time.time()
                response = await conversation_service.process_message(
                    user_id=user_id,
                    message=message,
                    context={"session_id": session_id, "message_number": i}
                )
                end_time = time.time()
                
                response_time = end_time - start_time
                response_times.append(response_time)
                
                assert response is not None
                
                # Small delay between messages
                await asyncio.sleep(0.1)
            
            return response_times
        
        # Create tasks for concurrent users
        tasks = [
            user_conversation(f"user_{i}")
            for i in range(num_users)
        ]
        
        # Run all user conversations concurrently
        start_time = time.time()
        all_response_times = await asyncio.gather(*tasks)
        end_time = time.time()
        
        total_time = end_time - start_time
        
        # Flatten response times
        flat_response_times = [rt for user_times in all_response_times for rt in user_times]
        
        avg_response_time = statistics.mean(flat_response_times)
        max_response_time = max(flat_response_times)
        
        print(f"Concurrent test - Total time: {total_time:.3f}s")
        print(f"Concurrent test - Average response time: {avg_response_time:.3f}s")
        print(f"Concurrent test - Max response time: {max_response_time:.3f}s")
        
        # Response times should still be reasonable under load
        assert avg_response_time < performance_thresholds["max_response_time"] * 2  # Allow 2x for concurrency
        assert max_response_time < performance_thresholds["max_response_time"] * 3  # Allow 3x for worst case


@pytest.mark.performance
class TestMemoryPerformance:
    """Test memory-related performance."""
    
    @pytest.mark.asyncio
    async def test_memory_storage_performance(self, memory_service, sample_user_id):
        """Test memory storage performance."""
        num_memories = 100
        
        start_time = time.time()
        
        for i in range(num_memories):
            await memory_service.store_memory(
                user_id=sample_user_id,
                content=f"Test memory {i} with some content to store",
                interaction_type="conversation"
            )
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time_per_memory = total_time / num_memories
        
        print(f"Stored {num_memories} memories in {total_time:.3f}s")
        print(f"Average time per memory: {avg_time_per_memory:.3f}s")
        
        # Should be able to store memories quickly
        assert avg_time_per_memory < 0.1  # 100ms per memory
    
    @pytest.mark.asyncio
    async def test_memory_retrieval_performance(self, memory_service, sample_user_id):
        """Test memory retrieval performance."""
        # First, store some memories
        for i in range(50):
            await memory_service.store_memory(
                user_id=sample_user_id,
                content=f"Memory about topic {i % 5} with content {i}",
                interaction_type="conversation"
            )
        
        # Test retrieval performance
        queries = ["topic 0", "topic 1", "topic 2", "content", "memory"]
        
        retrieval_times = []
        
        for query in queries:
            start_time = time.time()
            memories = await memory_service.retrieve_memories(
                user_id=sample_user_id,
                query=query,
                limit=10
            )
            end_time = time.time()
            
            retrieval_time = end_time - start_time
            retrieval_times.append(retrieval_time)
            
            assert memories is not None
            print(f"Retrieved {len(memories)} memories for '{query}' in {retrieval_time:.3f}s")
        
        avg_retrieval_time = statistics.mean(retrieval_times)
        print(f"Average retrieval time: {avg_retrieval_time:.3f}s")
        
        # Memory retrieval should be fast
        assert avg_retrieval_time < 0.5  # 500ms average


@pytest.mark.performance
@pytest.mark.slow
class TestLoadPerformance:
    """Test system performance under load."""
    
    @pytest.mark.asyncio
    async def test_sustained_load(self, conversation_service):
        """Test system performance under sustained load."""
        num_users = 10
        messages_per_user = 10
        duration_minutes = 1  # 1 minute test
        
        async def sustained_user_load(user_id: str):
            """Simulate sustained user activity."""
            session_id = f"load_test_{user_id}"
            response_times = []
            
            for i in range(messages_per_user):
                message = f"Load test message {i} from {user_id}"
                
                start_time = time.time()
                try:
                    response = await conversation_service.process_message(
                        user_id=user_id,
                        message=message,
                        context={"session_id": session_id, "message_number": i}
                    )
                    end_time = time.time()
                    
                    response_time = end_time - start_time
                    response_times.append(response_time)
                    
                    assert response is not None
                    
                except Exception as e:
                    print(f"Error in sustained load test: {e}")
                    response_times.append(float('inf'))  # Mark as failed
                
                # Wait between messages
                await asyncio.sleep(duration_minutes * 60 / messages_per_user)
            
            return response_times
        
        # Create tasks for all users
        tasks = [
            sustained_user_load(f"load_user_{i}")
            for i in range(num_users)
        ]
        
        # Run sustained load test
        start_time = time.time()
        all_response_times = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        total_duration = end_time - start_time
        
        # Process results
        successful_times = []
        failed_requests = 0
        
        for user_times in all_response_times:
            if isinstance(user_times, Exception):
                failed_requests += messages_per_user
                continue
            
            for rt in user_times:
                if rt == float('inf'):
                    failed_requests += 1
                else:
                    successful_times.append(rt)
        
        total_requests = num_users * messages_per_user
        success_rate = len(successful_times) / total_requests
        
        if successful_times:
            avg_response_time = statistics.mean(successful_times)
            max_response_time = max(successful_times)
        else:
            avg_response_time = float('inf')
            max_response_time = float('inf')
        
        print(f"Load test results:")
        print(f"  Duration: {total_duration:.1f}s")
        print(f"  Total requests: {total_requests}")
        print(f"  Successful requests: {len(successful_times)}")
        print(f"  Failed requests: {failed_requests}")
        print(f"  Success rate: {success_rate:.2%}")
        print(f"  Average response time: {avg_response_time:.3f}s")
        print(f"  Max response time: {max_response_time:.3f}s")
        
        # Performance assertions
        assert success_rate >= 0.95  # 95% success rate minimum
        if successful_times:
            assert avg_response_time < 2.0  # 2 second average under load
            assert max_response_time < 5.0  # 5 second max under load


@pytest.mark.performance
class TestCachePerformance:
    """Test caching performance."""
    
    @pytest.mark.asyncio
    async def test_cache_hit_performance(self, conversation_service, sample_user_id):
        """Test performance improvement from caching."""
        message = "What are some stress management techniques?"
        
        # First request (cache miss)
        start_time = time.time()
        response1 = await conversation_service.process_message(
            user_id=sample_user_id,
            message=message,
            context={"session_id": "cache_perf_1"}
        )
        first_response_time = time.time() - start_time
        
        # Second request (potential cache hit)
        start_time = time.time()
        response2 = await conversation_service.process_message(
            user_id=sample_user_id,
            message=message,
            context={"session_id": "cache_perf_2"}
        )
        second_response_time = time.time() - start_time
        
        print(f"First response time: {first_response_time:.3f}s")
        print(f"Second response time: {second_response_time:.3f}s")
        
        assert response1 is not None
        assert response2 is not None
        
        # Second response should be faster (or at least not significantly slower)
        # Note: This test might not always pass if caching isn't implemented
        # or if the cache doesn't apply to this specific scenario
        if second_response_time < first_response_time:
            improvement = (first_response_time - second_response_time) / first_response_time
            print(f"Cache improvement: {improvement:.1%}")
            assert improvement > 0  # Some improvement expected
