"""
Unit tests for the AI Companion System data models.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch

from ai_companion.core.models import (
    EmotionType, InteractionType, MemoryType,
    MemoryEntry, PersonalMemory, UniversalMemory,
    EmotionalState, UserProfile, ConversationMessage,
    Conversation, utc_now, generate_id,
    calculate_memory_importance
)


class TestEmotionType:
    """Test EmotionType enum."""
    
    def test_primary_emotions(self):
        """Test primary emotion types."""
        assert EmotionType.JOY == "joy"
        assert EmotionType.SADNESS == "sadness"
        assert EmotionType.ANGER == "anger"
        assert EmotionType.FEAR == "fear"
        assert EmotionType.NEUTRAL == "neutral"
    
    def test_secondary_emotions(self):
        """Test secondary emotion types."""
        assert EmotionType.ANXIETY == "anxiety"
        assert EmotionType.EXCITEMENT == "excitement"
        assert EmotionType.STRESS == "stress"
        assert EmotionType.LONELINESS == "loneliness"


class TestInteractionType:
    """Test InteractionType enum."""
    
    def test_interaction_types(self):
        """Test interaction type values."""
        assert InteractionType.CONVERSATION == "conversation"
        assert InteractionType.EMOTION == "emotion"
        assert InteractionType.CRISIS == "crisis"
        assert InteractionType.SUPPORT_SEEKING == "support_seeking"


class TestMemoryEntry:
    """Test MemoryEntry model."""
    
    def test_memory_entry_creation(self):
        """Test creating a memory entry."""
        memory = MemoryEntry(
            user_id="test_user",
            memory_type=MemoryType.PERSONAL,
            interaction_type=InteractionType.CONVERSATION,
            content="Test memory content"
        )
        
        assert memory.user_id == "test_user"
        assert memory.memory_type == MemoryType.PERSONAL
        assert memory.interaction_type == InteractionType.CONVERSATION
        assert memory.content == "Test memory content"
        assert memory.confidence == 1.0
        assert memory.importance == 0.5
        assert memory.access_count == 0
        assert isinstance(memory.created_at, datetime)
        assert isinstance(memory.last_accessed, datetime)
    
    def test_memory_entry_with_emotion(self):
        """Test creating a memory entry with emotion."""
        memory = MemoryEntry(
            user_id="test_user",
            memory_type=MemoryType.PERSONAL,
            interaction_type=InteractionType.EMOTION,
            content="User feeling anxious",
            emotion=EmotionType.ANXIETY,
            importance=0.8
        )
        
        assert memory.emotion == EmotionType.ANXIETY
        assert memory.importance == 0.8
    
    def test_content_hash_generation(self):
        """Test content hash generation."""
        memory = MemoryEntry(
            user_id="test_user",
            memory_type=MemoryType.PERSONAL,
            interaction_type=InteractionType.CONVERSATION,
            content="Test content"
        )
        
        hash1 = memory.get_content_hash()
        hash2 = memory.get_content_hash()
        
        assert hash1 == hash2  # Should be consistent
        assert len(hash1) == 32  # MD5 hash length
        assert memory.content_hash == hash1  # Should be cached
    
    def test_search_keywords_extraction(self):
        """Test search keywords extraction."""
        memory = MemoryEntry(
            user_id="test_user",
            memory_type=MemoryType.PERSONAL,
            interaction_type=InteractionType.CONVERSATION,
            content="User mentioned feeling anxious about work presentation tomorrow"
        )
        
        keywords = memory.get_search_keywords()
        
        assert isinstance(keywords, list)
        assert len(keywords) <= 10
        assert "anxious" in keywords
        assert "work" in keywords
        assert "presentation" in keywords
        assert "tomorrow" in keywords
        # Short words should be filtered out
        assert "a" not in keywords


class TestPersonalMemory:
    """Test PersonalMemory model."""
    
    def test_personal_memory_creation(self):
        """Test creating a personal memory."""
        memory = PersonalMemory(
            user_id="test_user",
            interaction_type=InteractionType.PREFERENCE,
            content="User prefers morning conversations",
            privacy_level="private"
        )
        
        assert memory.memory_type == MemoryType.PERSONAL
        assert memory.user_id == "test_user"
        assert memory.privacy_level == "private"
        assert memory.personal_goals == []
    
    def test_personal_memory_with_goals(self):
        """Test personal memory with goals."""
        memory = PersonalMemory(
            user_id="test_user",
            interaction_type=InteractionType.GOAL,
            content="User wants to reduce anxiety",
            personal_goals=["reduce_anxiety", "improve_sleep"]
        )
        
        assert "reduce_anxiety" in memory.personal_goals
        assert "improve_sleep" in memory.personal_goals


class TestUniversalMemory:
    """Test UniversalMemory model."""
    
    def test_universal_memory_creation(self):
        """Test creating a universal memory."""
        memory = UniversalMemory(
            interaction_type=InteractionType.CONVERSATION,
            content="General coping strategy for anxiety",
            topic_tags=["anxiety", "coping", "mental_health"]
        )
        
        assert memory.memory_type == MemoryType.UNIVERSAL
        assert memory.user_id == ""
        assert memory.global_relevance == 0.5
        assert memory.source_count == 1
        assert "anxiety" in memory.topic_tags


class TestEmotionalState:
    """Test EmotionalState model."""
    
    def test_emotional_state_creation(self):
        """Test creating an emotional state."""
        state = EmotionalState(
            primary_emotion=EmotionType.ANXIETY,
            intensity=0.7,
            confidence=0.85,
            valence=-0.3,
            arousal=0.6
        )
        
        assert state.primary_emotion == EmotionType.ANXIETY
        assert state.intensity == 0.7
        assert state.confidence == 0.85
        assert state.valence == -0.3
        assert state.arousal == 0.6
        assert isinstance(state.timestamp, datetime)
    
    def test_emotional_state_with_secondary_emotions(self):
        """Test emotional state with secondary emotions."""
        state = EmotionalState(
            primary_emotion=EmotionType.ANXIETY,
            secondary_emotions={
                EmotionType.FEAR: 0.4,
                EmotionType.STRESS: 0.6
            }
        )
        
        assert state.secondary_emotions[EmotionType.FEAR] == 0.4
        assert state.secondary_emotions[EmotionType.STRESS] == 0.6
    
    def test_get_dominant_emotions(self):
        """Test getting dominant emotions."""
        state = EmotionalState(
            primary_emotion=EmotionType.ANXIETY,
            secondary_emotions={
                EmotionType.FEAR: 0.4,
                EmotionType.STRESS: 0.6,
                EmotionType.SADNESS: 0.2
            }
        )
        
        # Default threshold is 0.3
        dominant = state.get_dominant_emotions()
        assert EmotionType.ANXIETY in dominant
        assert EmotionType.FEAR in dominant
        assert EmotionType.STRESS in dominant
        assert EmotionType.SADNESS not in dominant
        
        # Custom threshold
        dominant_high = state.get_dominant_emotions(threshold=0.5)
        assert EmotionType.ANXIETY in dominant_high
        assert EmotionType.STRESS in dominant_high
        assert EmotionType.FEAR not in dominant_high


class TestUserProfile:
    """Test UserProfile model."""
    
    def test_user_profile_creation(self):
        """Test creating a user profile."""
        profile = UserProfile(
            user_id="test_user",
            name="Test User",
            preferences={"communication_style": "supportive"},
            interests=["mental_health", "productivity"]
        )
        
        assert profile.user_id == "test_user"
        assert profile.name == "Test User"
        assert profile.preferences["communication_style"] == "supportive"
        assert "mental_health" in profile.interests
        assert profile.interaction_count == 0
        assert isinstance(profile.created_at, datetime)


class TestConversationMessage:
    """Test ConversationMessage model."""
    
    def test_message_creation(self):
        """Test creating a conversation message."""
        message = ConversationMessage(
            user_id="test_user",
            role="user",
            content="Hello, how are you?",
            emotion=EmotionType.NEUTRAL
        )
        
        assert message.user_id == "test_user"
        assert message.role == "user"
        assert message.content == "Hello, how are you?"
        assert message.emotion == EmotionType.NEUTRAL
        assert message.confidence == 1.0
        assert isinstance(message.timestamp, datetime)


class TestConversation:
    """Test Conversation model."""
    
    def test_conversation_creation(self):
        """Test creating a conversation."""
        conversation = Conversation(
            user_id="test_user",
            topics=["greeting", "mood"],
            emotions=[EmotionType.NEUTRAL, EmotionType.JOY]
        )
        
        assert conversation.user_id == "test_user"
        assert "greeting" in conversation.topics
        assert EmotionType.NEUTRAL in conversation.emotions
        assert len(conversation.messages) == 0
        assert isinstance(conversation.start_time, datetime)
        assert conversation.end_time is None


class TestUtilityFunctions:
    """Test utility functions."""
    
    def test_utc_now(self):
        """Test UTC now function."""
        now = utc_now()
        assert isinstance(now, datetime)
        assert now.tzinfo == timezone.utc
    
    def test_generate_id(self):
        """Test ID generation."""
        id1 = generate_id()
        id2 = generate_id()
        
        assert isinstance(id1, str)
        assert isinstance(id2, str)
        assert id1 != id2
        assert len(id1) == 36  # UUID4 length with hyphens
    
    def test_calculate_memory_importance(self):
        """Test memory importance calculation."""
        # Test with emotion
        importance = calculate_memory_importance(
            emotion=EmotionType.ANXIETY,
            interaction_type=InteractionType.EMOTION,
            user_frequency=10,
            content_length=100
        )
        
        assert 0.0 <= importance <= 1.0
        assert importance > 0.5  # Should be above base importance
        
        # Test without emotion
        importance_no_emotion = calculate_memory_importance(
            emotion=None,
            interaction_type=InteractionType.CONVERSATION,
            user_frequency=5,
            content_length=50
        )
        
        assert 0.0 <= importance_no_emotion <= 1.0
        
        # Crisis should have high importance
        crisis_importance = calculate_memory_importance(
            emotion=EmotionType.SADNESS,
            interaction_type=InteractionType.CRISIS,
            user_frequency=1,
            content_length=200
        )
        
        assert crisis_importance > importance
