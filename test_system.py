#!/usr/bin/env python3
"""
System Validation Test for AI Companion System
Comprehensive test to validate all system components are working correctly.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Dict, Any, List

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from ai_companion.config.settings import settings, validate_settings
    from ai_companion.services.gemini import GeminiService
    from ai_companion.services.storage import StorageService
    from ai_companion.core.memory import MemoryService
    from ai_companion.core.emotions import EmotionalIntelligenceService
    from ai_companion.core.conversation import ConversationService
    from ai_companion.mental_health.crisis import CrisisDetectionService
    from ai_companion.mental_health.analytics import MentalHealthAnalytics
    from ai_companion.main import AICompanionSystem
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Make sure you've installed dependencies: pip install -r requirements.txt")
    sys.exit(1)


class SystemValidator:
    """Comprehensive system validation."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.test_results: Dict[str, bool] = {}
        self.test_details: Dict[str, str] = {}
        
    def print_header(self, title: str):
        """Print test section header."""
        print(f"\n{'='*60}")
        print(f"🧪 {title}")
        print(f"{'='*60}")
    
    def print_test(self, test_name: str, success: bool, details: str = ""):
        """Print individual test result."""
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")
        
        self.test_results[test_name] = success
        self.test_details[test_name] = details
    
    async def test_configuration(self) -> bool:
        """Test configuration validation."""
        self.print_header("Configuration Tests")
        
        try:
            validate_settings()
            self.print_test("Configuration Validation", True, "All settings valid")
            return True
        except Exception as e:
            self.print_test("Configuration Validation", False, str(e))
            return False
    
    async def test_core_services(self) -> bool:
        """Test core service initialization."""
        self.print_header("Core Service Tests")
        
        all_passed = True
        
        # Test Gemini Service
        try:
            gemini_service = GeminiService()
            self.print_test("Gemini Service Init", True, f"Model: {settings.gemini_model}")
        except Exception as e:
            self.print_test("Gemini Service Init", False, str(e))
            all_passed = False
        
        # Test Storage Service
        try:
            storage_service = StorageService()
            self.print_test("Storage Service Init", True, f"DB: {settings.database_path}")
        except Exception as e:
            self.print_test("Storage Service Init", False, str(e))
            all_passed = False
        
        # Test Memory Service
        try:
            if 'storage_service' in locals():
                memory_service = MemoryService(storage_service)
                self.print_test("Memory Service Init", True, "Dual-memory architecture ready")
            else:
                raise Exception("Storage service not available")
        except Exception as e:
            self.print_test("Memory Service Init", False, str(e))
            all_passed = False
        
        return all_passed
    
    async def test_ai_services(self) -> bool:
        """Test AI-powered services."""
        self.print_header("AI Service Tests")
        
        all_passed = True
        
        try:
            # Initialize services
            gemini_service = GeminiService()
            storage_service = StorageService()
            memory_service = MemoryService(storage_service)
            emotional_intelligence = EmotionalIntelligenceService(gemini_service)
            
            # Test emotional intelligence
            emotion_result = await emotional_intelligence.analyze_emotion(
                "I'm feeling happy today!", "test_user"
            )
            self.print_test("Emotion Analysis", True, 
                          f"Detected: {emotion_result.primary_emotion.value}")
            
            # Test conversation service
            conversation_service = ConversationService(
                memory_service, emotional_intelligence, gemini_service
            )
            
            # Test basic conversation
            response = await conversation_service.process_message(
                user_id="test_user",
                message="Hello, how are you?",
                context={"test": True}
            )
            
            if response and "response" in response:
                self.print_test("Conversation Processing", True, 
                              f"Response length: {len(response['response'])} chars")
            else:
                self.print_test("Conversation Processing", False, "No response generated")
                all_passed = False
                
        except Exception as e:
            self.print_test("AI Services", False, str(e))
            all_passed = False
        
        return all_passed
    
    async def test_mental_health_services(self) -> bool:
        """Test mental health specific services."""
        self.print_header("Mental Health Service Tests")
        
        all_passed = True
        
        try:
            # Initialize services
            gemini_service = GeminiService()
            storage_service = StorageService()
            emotional_intelligence = EmotionalIntelligenceService(gemini_service)
            
            # Test crisis detection
            crisis_detection = CrisisDetectionService(emotional_intelligence)
            crisis_result = await crisis_detection.analyze_message(
                "I'm feeling a bit sad today", "test_user"
            )
            
            self.print_test("Crisis Detection", True, 
                          f"Risk level: {crisis_result.get('risk_level', 'unknown')}")
            
            # Test analytics
            analytics = MentalHealthAnalytics(storage_service)
            self.print_test("Analytics Service", True, "Service initialized")
            
        except Exception as e:
            self.print_test("Mental Health Services", False, str(e))
            all_passed = False
        
        return all_passed
    
    async def test_full_system(self) -> bool:
        """Test full system integration."""
        self.print_header("Full System Integration Test")
        
        try:
            # Initialize full system
            system = AICompanionSystem()
            await system.initialize()
            
            self.print_test("System Initialization", True, "All components ready")
            
            # Test system metrics
            metrics = system.get_system_metrics()
            self.print_test("System Metrics", True, 
                          f"Ready: {metrics['system_ready']}")
            
            # Cleanup
            await system.shutdown()
            self.print_test("System Shutdown", True, "Clean shutdown completed")
            
            return True
            
        except Exception as e:
            self.print_test("Full System Test", False, str(e))
            return False
    
    def print_summary(self):
        """Print test summary."""
        self.print_header("Test Summary")
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        failed_tests = total_tests - passed_tests
        
        print(f"📊 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n🔍 Failed Tests:")
            for test_name, success in self.test_results.items():
                if not success:
                    print(f"   ❌ {test_name}: {self.test_details[test_name]}")
        
        return failed_tests == 0


async def main():
    """Main test function."""
    print("🚀 AI Companion System - System Validation")
    print("=" * 60)
    
    validator = SystemValidator()
    
    # Run all tests
    config_ok = await validator.test_configuration()
    core_ok = await validator.test_core_services()
    ai_ok = await validator.test_ai_services()
    mental_health_ok = await validator.test_mental_health_services()
    system_ok = await validator.test_full_system()
    
    # Print summary
    all_passed = validator.print_summary()
    
    if all_passed:
        print(f"\n🎉 All tests passed! System is ready for use.")
        return 0
    else:
        print(f"\n⚠️ Some tests failed. Please check the issues above.")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
