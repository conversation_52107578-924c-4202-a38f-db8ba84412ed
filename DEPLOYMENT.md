# 🚀 AI Companion System - Production Deployment Guide

This guide covers deploying the AI Companion System to production environments.

## 📋 Pre-Deployment Checklist

### **1. System Requirements**
- **Python 3.9+** (3.11 recommended)
- **4GB+ RAM** (8GB recommended for production)
- **2+ CPU cores**
- **10GB+ disk space**
- **SSL certificate** (for HTTPS)

### **2. Security Requirements**
- [ ] Generate secure SECRET_KEY (32+ characters)
- [ ] Configure proper CORS origins
- [ ] Set up rate limiting
- [ ] Enable HTTPS/TLS
- [ ] Configure firewall rules
- [ ] Set up monitoring and logging

### **3. Environment Setup**
- [ ] Create production .env file
- [ ] Configure database (PostgreSQL recommended)
- [ ] Set up Redis for caching
- [ ] Configure backup strategy
- [ ] Set up monitoring tools

## 🔧 Production Configuration

### **1. Environment Variables**

Create a production `.env` file:

```bash
# Production Environment
ENVIRONMENT=production
DEBUG_MODE=false
LOG_LEVEL=INFO

# Security (REQUIRED - Generate secure values)
SECRET_KEY=your_secure_32_character_secret_key_here
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
CORS_ORIGINS=https://yourdomain.com

# API Configuration
GEMINI_API_KEY=your_production_gemini_api_key
API_KEY_REQUIRED=true
API_KEY=your_secure_api_key

# Database (PostgreSQL recommended)
DATABASE_URL=postgresql://user:password@localhost:5432/ai_companion

# Redis (Recommended for production)
REDIS_ENABLED=true
REDIS_URL=redis://localhost:6379

# Performance
CACHE_SIZE=5000
THREAD_POOL_SIZE=8
MEMORY_THRESHOLD_MB=1000

# Mental Health Settings
CRISIS_THRESHOLD=0.7
ENABLE_MENTAL_HEALTH_PLATFORM=true
ANONYMIZATION_ENABLED=true

# Monitoring
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30
```

### **2. Database Setup**

#### **PostgreSQL (Recommended)**

```bash
# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Create database and user
sudo -u postgres psql
CREATE DATABASE ai_companion;
CREATE USER ai_companion WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE ai_companion TO ai_companion;
\q

# Update DATABASE_URL in .env
DATABASE_URL=postgresql://ai_companion:secure_password@localhost:5432/ai_companion
```

#### **Redis Setup**

```bash
# Install Redis
sudo apt-get install redis-server

# Configure Redis
sudo systemctl enable redis-server
sudo systemctl start redis-server

# Test connection
redis-cli ping
```

## 🐳 Docker Deployment

### **1. Using Docker Compose (Recommended)**

```bash
# Clone repository
git clone https://github.com/your-username/ai-companion-system.git
cd ai-companion-system

# Create production environment file
cp .env.example .env.production
# Edit .env.production with your production values

# Deploy with Docker Compose
docker-compose -f docker-compose.yml --env-file .env.production up -d

# Check status
docker-compose ps
docker-compose logs ai-companion
```

### **2. Manual Docker Deployment**

```bash
# Build image
docker build -t ai-companion-system .

# Run container
docker run -d \
  --name ai-companion \
  -p 80:7860 \
  -p 8000:8000 \
  --env-file .env.production \
  -v ./data:/app/data \
  --restart unless-stopped \
  ai-companion-system
```

## 🌐 Reverse Proxy Setup (Nginx)

### **1. Install Nginx**

```bash
sudo apt-get install nginx
```

### **2. Configure Nginx**

Create `/etc/nginx/sites-available/ai-companion`:

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # Gradio Web Interface
    location / {
        proxy_pass http://localhost:7860;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support for Gradio
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # API Endpoints
    location /api/ {
        proxy_pass http://localhost:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Health check
    location /health {
        proxy_pass http://localhost:8000/health;
        access_log off;
    }
}
```

### **3. Enable Site**

```bash
sudo ln -s /etc/nginx/sites-available/ai-companion /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 📊 Monitoring and Logging

### **1. System Monitoring**

```bash
# Install monitoring tools
sudo apt-get install htop iotop

# Monitor system resources
htop
iotop

# Check disk usage
df -h
du -sh data/
```

### **2. Application Logs**

```bash
# View application logs
tail -f data/logs/ai_companion.log

# View Docker logs
docker-compose logs -f ai-companion

# Monitor performance
tail -f data/logs/performance.log
```

### **3. Health Checks**

```bash
# Manual health check
curl https://yourdomain.com/health

# Automated monitoring (add to crontab)
*/5 * * * * curl -f https://yourdomain.com/health || echo "Health check failed" | mail -s "AI Companion Down" <EMAIL>
```

## 🔒 Security Hardening

### **1. Firewall Configuration**

```bash
# Configure UFW firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 7860/tcp  # Block direct access to Gradio
sudo ufw deny 8000/tcp  # Block direct access to API
```

### **2. SSL/TLS Setup**

```bash
# Using Let's Encrypt (Certbot)
sudo apt-get install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### **3. Regular Updates**

```bash
# System updates
sudo apt-get update && sudo apt-get upgrade

# Application updates
git pull origin main
docker-compose build --no-cache
docker-compose up -d
```

## 📈 Performance Optimization

### **1. Database Optimization**

```sql
-- PostgreSQL optimizations
-- Add indexes for frequently queried columns
CREATE INDEX idx_memories_user_id ON memories(user_id);
CREATE INDEX idx_conversations_timestamp ON conversations(created_at);

-- Analyze and vacuum regularly
ANALYZE;
VACUUM;
```

### **2. Redis Configuration**

```bash
# Edit /etc/redis/redis.conf
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### **3. Application Tuning**

```bash
# In .env.production
CACHE_SIZE=10000
THREAD_POOL_SIZE=16
TARGET_RESPONSE_TIME=0.2
REDIS_ENABLED=true
```

## 🚨 Troubleshooting

### **Common Issues**

1. **High Memory Usage**
   - Reduce CACHE_SIZE
   - Enable Redis caching
   - Monitor memory leaks

2. **Slow Response Times**
   - Check Gemini API quota
   - Optimize database queries
   - Increase THREAD_POOL_SIZE

3. **Database Connection Issues**
   - Check DATABASE_URL
   - Verify database credentials
   - Check connection limits

### **Emergency Procedures**

```bash
# Quick restart
docker-compose restart ai-companion

# Full system restart
docker-compose down
docker-compose up -d

# Rollback to previous version
git checkout previous-tag
docker-compose build --no-cache
docker-compose up -d
```

## 📞 Support

For production support:
- Check logs: `data/logs/ai_companion.log`
- Run health check: `make health-check`
- Monitor system: `htop` and `docker stats`
- Contact: [Your support contact]
