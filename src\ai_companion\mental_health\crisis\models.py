"""
Crisis Detection Models and Data Structures.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum

from ...core.models import MentalHealthRisk


class CrisisType(Enum):
    """Types of mental health crises."""
    SUICIDAL_IDEATION = "suicidal_ideation"
    SELF_HARM = "self_harm"
    SEVERE_DEPRESSION = "severe_depression"
    PANIC_ATTACK = "panic_attack"
    PSYCHOTIC_EPISODE = "psychotic_episode"
    SUBSTANCE_ABUSE = "substance_abuse"
    DOMESTIC_VIOLENCE = "domestic_violence"
    EATING_DISORDER = "eating_disorder"
    GENERAL_DISTRESS = "general_distress"


class InterventionType(Enum):
    """Types of crisis interventions."""
    IMMEDIATE_SUPPORT = "immediate_support"
    RESOURCE_REFERRAL = "resource_referral"
    SAFETY_PLANNING = "safety_planning"
    FOLLOW_UP_REQUIRED = "follow_up_required"
    EMERGENCY_CONTACT = "emergency_contact"


@dataclass
class CrisisEvent:
    """Data class for crisis events."""
    event_id: str
    user_id: str
    crisis_type: Optional[CrisisType]
    risk_level: MentalHealthRisk
    crisis_score: float
    message_content: str
    detected_patterns: List[str]
    intervention_triggered: bool
    intervention_type: Optional[InterventionType]
    resources_provided: List[Dict[str, str]]
    timestamp: datetime
    follow_up_scheduled: bool
    resolved: bool = False


@dataclass
class CrisisPattern:
    """Crisis detection pattern."""
    pattern_id: str
    keywords: List[str]
    severity_weight: float
    crisis_type: CrisisType
    description: str
    requires_immediate_action: bool


@dataclass
class InterventionStrategy:
    """Crisis intervention strategy."""
    strategy_id: str
    crisis_type: CrisisType
    immediate_response: str
    techniques: List[str]
    resources: List[str]
    follow_up_required: bool
    safety_planning: bool


@dataclass
class CrisisResource:
    """Crisis support resource."""
    resource_id: str
    name: str
    description: str
    contact_info: str
    availability: str
    crisis_types: List[CrisisType]
    is_emergency: bool
    location: Optional[str] = None
