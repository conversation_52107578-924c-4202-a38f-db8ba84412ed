[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-companion-system"
version = "1.0.0"
description = "A conversational AI system for emotional support and mental health"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "AI Companion Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "AI Companion Team", email = "<EMAIL>"}
]
keywords = [
    "ai", "chatbot", "mental-health", "emotional-intelligence", 
    "conversation", "therapy", "support", "gemini"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Healthcare Industry",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.9"
dependencies = [
    "google-generativeai>=0.3.0,<1.0.0",
    "sentence-transformers>=2.2.0,<3.0.0",
    "torch>=2.0.0,<3.0.0",
    "transformers>=4.30.0,<5.0.0",
    "fastapi>=0.100.0,<1.0.0",
    "uvicorn[standard]>=0.23.0,<1.0.0",
    "gradio>=4.0.0,<5.0.0",
    "redis>=4.5.0,<6.0.0",
    "sqlalchemy>=2.0.0,<3.0.0",
    "alembic>=1.11.0,<2.0.0",
    "pandas>=2.0.0,<3.0.0",
    "numpy>=1.24.0,<2.0.0",
    "scikit-learn>=1.3.0,<2.0.0",
    "python-dotenv>=1.0.0,<2.0.0",
    "pydantic>=2.0.0,<3.0.0",
    "pydantic-settings>=2.0.0,<3.0.0",
    "httpx>=0.24.0,<1.0.0",
    "requests>=2.31.0,<3.0.0",
    "psutil>=5.9.0,<6.0.0",
    "cryptography>=41.0.0,<42.0.0",
    "python-multipart>=0.0.6,<1.0.0",
    "python-dateutil>=2.8.0,<3.0.0",
    "pytz>=2023.3",
]

[project.optional-dependencies]
whatsapp = ["twilio>=8.10.0,<9.0.0"]
nlp = ["nltk>=3.8.0,<4.0.0"]
visualization = ["matplotlib>=3.7.0,<4.0.0", "seaborn>=0.12.0,<1.0.0"]
dev = [
    "pytest>=7.4.0,<8.0.0",
    "pytest-asyncio>=0.21.0,<1.0.0",
    "pytest-cov>=4.1.0,<5.0.0",
    "pytest-mock>=3.11.0,<4.0.0",
    "black>=23.0.0,<24.0.0",
    "flake8>=6.0.0,<7.0.0",
    "isort>=5.12.0,<6.0.0",
    "mypy>=1.5.0,<2.0.0",
    "mkdocs>=1.5.0,<2.0.0",
    "mkdocs-material>=9.0.0,<10.0.0",
    "ipython>=8.0.0,<9.0.0",
    "jupyter>=1.0.0,<2.0.0",
]

[project.urls]
Homepage = "https://github.com/ai-companion/ai-companion-system"
Documentation = "https://ai-companion.readthedocs.io/"
Repository = "https://github.com/ai-companion/ai-companion-system.git"
"Bug Tracker" = "https://github.com/ai-companion/ai-companion-system/issues"

[project.scripts]
ai-companion = "ai_companion.main:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
ai_companion = ["py.typed"]

[tool.black]
line-length = 100
target-version = ['py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.flake8]
max-line-length = 100
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    ".eggs",
    "*.egg",
]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "gradio.*",
    "redis.*",
    "sentence_transformers.*",
    "torch.*",
    "transformers.*",
    "google.generativeai.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "performance: marks tests as performance tests",
]

[tool.coverage.run]
source = ["src/ai_companion"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
