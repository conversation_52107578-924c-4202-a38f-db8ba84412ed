# AI Companion System - Development Docker Compose
# This configuration is optimized for development with hot reloading and debugging

version: '3.8'

services:
  # Main AI Companion Application
  ai-companion:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "7860:7860"  # Gradio interface
      - "8000:8000"  # FastAPI
      - "5678:5678"  # Debug port
    volumes:
      - ./src:/app/src
      - ./data:/app/data
      - ./tests:/app/tests
      - ./.env:/app/.env
    environment:
      - ENVIRONMENT=development
      - DEBUG_MODE=true
      - REDIS_ENABLED=true
      - REDIS_HOST=redis
      - DATABASE_PATH=/app/data/db/ai_companion.db
    depends_on:
      - redis
    restart: unless-stopped
    command: python -m ai_companion.main
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL (optional, for production-like testing)
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: ai_companion
      POSTGRES_USER: ai_companion
      POSTGRES_PASSWORD: dev_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai_companion"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Jupyter Notebook for development and analysis
  jupyter:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8888:8888"
    volumes:
      - ./src:/app/src
      - ./data:/app/data
      - ./notebooks:/app/notebooks
      - ./.env:/app/.env
    environment:
      - JUPYTER_ENABLE_LAB=yes
    command: jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token=''
    restart: unless-stopped

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:
  prometheus_data:
  grafana_data:

networks:
  default:
    name: ai-companion-dev
