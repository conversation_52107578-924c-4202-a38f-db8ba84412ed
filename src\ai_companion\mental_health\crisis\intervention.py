"""
Crisis Intervention Management.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from .models import CrisisType, InterventionType, CrisisEvent, InterventionStrategy
from ...core.models import MentalHealthRisk, TherapeuticTechnique, utc_now, generate_id
from ...core.emotions import EmotionalIntelligenceService


class InterventionManager:
    """Manages crisis interventions and responses."""
    
    def __init__(self, emotional_intelligence: EmotionalIntelligenceService):
        """Initialize intervention manager."""
        self.logger = logging.getLogger(__name__)
        self.emotional_intelligence = emotional_intelligence
        self.intervention_strategies = self._initialize_intervention_strategies()
        self.active_interventions: Dict[str, CrisisEvent] = {}
        
    def _initialize_intervention_strategies(self) -> Dict[str, Dict[str, Any]]:
        """Initialize intervention strategies by crisis type."""
        return {
            "suicidal_ideation": {
                "immediate_response": "I'm very concerned about what you've shared. Your life has value and there are people who want to help. Please reach out to a crisis helpline immediately.",
                "techniques": [TherapeuticTechnique.VALIDATION, TherapeuticTechnique.SAFETY_PLANNING],
                "resources": ["suicide_prevention_hotline", "emergency_services", "crisis_text_line"],
                "follow_up_required": True,
                "safety_planning": True,
                "escalation_threshold": 0.8
            },
            "self_harm": {
                "immediate_response": "I'm worried about you and want to help. Self-harm might provide temporary relief, but there are healthier ways to cope with these feelings.",
                "techniques": [TherapeuticTechnique.VALIDATION, TherapeuticTechnique.COPING_STRATEGIES],
                "resources": ["self_harm_support", "mental_health_services", "crisis_text_line"],
                "follow_up_required": True,
                "safety_planning": True,
                "escalation_threshold": 0.7
            },
            "severe_depression": {
                "immediate_response": "I hear how much pain you're in right now. These feelings are overwhelming, but they can change with the right support.",
                "techniques": [TherapeuticTechnique.VALIDATION, TherapeuticTechnique.HOPE_INSTILLATION],
                "resources": ["mental_health_services", "depression_support", "therapy_referral"],
                "follow_up_required": True,
                "safety_planning": False,
                "escalation_threshold": 0.6
            },
            "panic_attack": {
                "immediate_response": "You're experiencing intense anxiety right now. Let's focus on your breathing. Try to breathe slowly and deeply.",
                "techniques": [TherapeuticTechnique.BREATHING_EXERCISES, TherapeuticTechnique.GROUNDING],
                "resources": ["anxiety_support", "breathing_exercises", "mindfulness_resources"],
                "follow_up_required": False,
                "safety_planning": False,
                "escalation_threshold": 0.5
            },
            "substance_abuse": {
                "immediate_response": "Struggling with substance use is challenging, and it takes courage to acknowledge it. There are people and resources that can help.",
                "techniques": [TherapeuticTechnique.VALIDATION, TherapeuticTechnique.MOTIVATIONAL_INTERVIEWING],
                "resources": ["addiction_services", "support_groups", "detox_centers"],
                "follow_up_required": True,
                "safety_planning": False,
                "escalation_threshold": 0.7
            },
            "domestic_violence": {
                "immediate_response": "Your safety is the most important thing. If you're in immediate danger, please contact emergency services or a domestic violence hotline.",
                "techniques": [TherapeuticTechnique.VALIDATION, TherapeuticTechnique.SAFETY_PLANNING],
                "resources": ["domestic_violence_hotline", "emergency_services", "safe_shelter"],
                "follow_up_required": True,
                "safety_planning": True,
                "escalation_threshold": 0.8
            },
            "eating_disorder": {
                "immediate_response": "Eating disorders are serious but treatable conditions. You deserve support and recovery is possible.",
                "techniques": [TherapeuticTechnique.VALIDATION, TherapeuticTechnique.PSYCHOEDUCATION],
                "resources": ["eating_disorder_support", "nutrition_counseling", "therapy_referral"],
                "follow_up_required": True,
                "safety_planning": False,
                "escalation_threshold": 0.6
            }
        }
    
    async def generate_intervention_response(
        self,
        crisis_type: Optional[str],
        crisis_score: float,
        user_id: str,
        message: str
    ) -> Dict[str, Any]:
        """Generate appropriate intervention response."""
        try:
            # Get intervention strategy
            strategy = self.intervention_strategies.get(crisis_type, {})
            base_response = strategy.get("immediate_response", "I'm here to support you.")
            
            # Use emotional intelligence for personalized response
            techniques = strategy.get("techniques", [TherapeuticTechnique.VALIDATION])
            primary_technique = techniques[0] if techniques else TherapeuticTechnique.VALIDATION
            
            # Generate empathetic response using AI
            ai_response = await self.emotional_intelligence.generate_therapeutic_response(
                message=message,
                user_id=user_id,
                technique=primary_technique,
                context={"crisis_detected": True, "crisis_type": crisis_type}
            )
            
            # Combine base response with AI-generated response
            if ai_response and ai_response.get("response"):
                combined_response = f"{base_response}\n\n{ai_response['response']}"
            else:
                combined_response = base_response
            
            # Determine intervention type
            intervention_type = self._determine_intervention_type(crisis_type, crisis_score)
            
            return {
                "response": combined_response,
                "intervention_type": intervention_type,
                "techniques_used": techniques,
                "requires_follow_up": strategy.get("follow_up_required", False),
                "safety_planning_needed": strategy.get("safety_planning", False),
                "escalation_needed": crisis_score >= strategy.get("escalation_threshold", 0.8),
                "timestamp": utc_now()
            }
            
        except Exception as e:
            self.logger.error(f"Error generating intervention response: {e}")
            return {
                "response": "I'm here to support you. Please consider reaching out to a mental health professional if you're struggling.",
                "intervention_type": InterventionType.IMMEDIATE_SUPPORT,
                "error": str(e)
            }
    
    def _determine_intervention_type(
        self,
        crisis_type: Optional[str],
        crisis_score: float
    ) -> InterventionType:
        """Determine the appropriate intervention type."""
        if crisis_score >= 0.8:
            return InterventionType.EMERGENCY_CONTACT
        elif crisis_score >= 0.6:
            return InterventionType.SAFETY_PLANNING
        elif crisis_score >= 0.4:
            return InterventionType.RESOURCE_REFERRAL
        else:
            return InterventionType.IMMEDIATE_SUPPORT
    
    async def handle_crisis_intervention(
        self,
        user_id: str,
        crisis_event: CrisisEvent
    ) -> Dict[str, Any]:
        """Handle the complete crisis intervention workflow."""
        try:
            # Store the crisis event
            self.active_interventions[crisis_event.event_id] = crisis_event
            
            # Generate intervention response
            intervention_response = await self.generate_intervention_response(
                crisis_event.crisis_type.value if crisis_event.crisis_type else None,
                crisis_event.crisis_score,
                user_id,
                crisis_event.message_content
            )
            
            # Schedule follow-up if needed
            if intervention_response.get("requires_follow_up"):
                await self._schedule_follow_up(
                    user_id,
                    crisis_event.crisis_type.value if crisis_event.crisis_type else "general"
                )
                crisis_event.follow_up_scheduled = True
            
            # Update crisis event
            crisis_event.intervention_triggered = True
            crisis_event.intervention_type = intervention_response.get("intervention_type")
            
            self.logger.info(f"Crisis intervention handled for user {user_id}")
            
            return {
                "intervention_successful": True,
                "response": intervention_response.get("response"),
                "intervention_type": intervention_response.get("intervention_type"),
                "follow_up_scheduled": crisis_event.follow_up_scheduled,
                "crisis_event_id": crisis_event.event_id
            }
            
        except Exception as e:
            self.logger.error(f"Error handling crisis intervention: {e}")
            return {
                "intervention_successful": False,
                "error": str(e)
            }
    
    async def _schedule_follow_up(self, user_id: str, crisis_type: str):
        """Schedule follow-up for crisis situations."""
        try:
            # In a real implementation, this would integrate with a scheduling system
            follow_up_time = utc_now() + timedelta(hours=24)
            
            self.logger.info(f"Follow-up scheduled for user {user_id} at {follow_up_time}")
            
            # Store follow-up information
            follow_up_data = {
                "user_id": user_id,
                "crisis_type": crisis_type,
                "scheduled_time": follow_up_time,
                "status": "scheduled"
            }
            
            # In production, this would be stored in a database or queue
            # For now, we'll just log it
            self.logger.info(f"Follow-up data: {follow_up_data}")
            
        except Exception as e:
            self.logger.error(f"Error scheduling follow-up: {e}")
    
    def get_intervention_strategy(self, crisis_type: str) -> Optional[Dict[str, Any]]:
        """Get intervention strategy for a crisis type."""
        return self.intervention_strategies.get(crisis_type)
    
    def update_intervention_strategy(
        self,
        crisis_type: str,
        strategy_updates: Dict[str, Any]
    ):
        """Update an intervention strategy."""
        if crisis_type in self.intervention_strategies:
            self.intervention_strategies[crisis_type].update(strategy_updates)
            self.logger.info(f"Updated intervention strategy for {crisis_type}")
        else:
            self.logger.warning(f"Crisis type {crisis_type} not found")
    
    def get_active_interventions(self) -> Dict[str, CrisisEvent]:
        """Get all active interventions."""
        return self.active_interventions.copy()
    
    def resolve_intervention(self, event_id: str):
        """Mark an intervention as resolved."""
        if event_id in self.active_interventions:
            self.active_interventions[event_id].resolved = True
            self.logger.info(f"Intervention {event_id} marked as resolved")
        else:
            self.logger.warning(f"Intervention {event_id} not found")
    
    def get_intervention_stats(self) -> Dict[str, Any]:
        """Get intervention statistics."""
        total_interventions = len(self.active_interventions)
        resolved_interventions = sum(
            1 for event in self.active_interventions.values() if event.resolved
        )
        
        crisis_type_counts = {}
        for event in self.active_interventions.values():
            if event.crisis_type:
                crisis_type = event.crisis_type.value
                crisis_type_counts[crisis_type] = crisis_type_counts.get(crisis_type, 0) + 1
        
        return {
            "total_interventions": total_interventions,
            "resolved_interventions": resolved_interventions,
            "active_interventions": total_interventions - resolved_interventions,
            "crisis_type_distribution": crisis_type_counts,
            "timestamp": utc_now()
        }
