"""
Crisis Pattern Detection Logic.
"""

import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict

from .models import CrisisType, CrisisPattern
from ...core.models import MentalHealthRisk
from ...core.models import EmotionalState
from ...core.emotions import EmotionalIntelligenceService


class CrisisDetector:
    """Handles crisis pattern detection and scoring."""
    
    def __init__(self, emotional_intelligence: EmotionalIntelligenceService):
        """Initialize crisis detector."""
        self.logger = logging.getLogger(__name__)
        self.emotional_intelligence = emotional_intelligence
        self.crisis_patterns = self._initialize_crisis_patterns()
        
    def _initialize_crisis_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Initialize crisis detection patterns with severity weights."""
        return {
            "suicidal_ideation": {
                "keywords": [
                    "kill myself", "end it all", "suicide", "suicidal", "want to die",
                    "better off dead", "no point living", "can't go on", "end my life",
                    "take my own life", "not worth living", "want to disappear forever"
                ],
                "severity_weight": 1.0,
                "requires_immediate": True
            },
            "self_harm": {
                "keywords": [
                    "cut myself", "hurt myself", "self harm", "cutting", "burning myself",
                    "hitting myself", "scratching", "self injury", "harm myself",
                    "punish myself", "deserve pain"
                ],
                "severity_weight": 0.9,
                "requires_immediate": True
            },
            "severe_depression": {
                "keywords": [
                    "hopeless", "worthless", "useless", "failure", "can't cope",
                    "everything is pointless", "no hope", "completely alone",
                    "nothing matters", "empty inside", "numb", "can't feel anything"
                ],
                "severity_weight": 0.7,
                "requires_immediate": False
            },
            "panic_attack": {
                "keywords": [
                    "can't breathe", "heart racing", "panic attack", "chest tight",
                    "dizzy", "shaking", "sweating", "feel like dying", "losing control",
                    "going crazy", "can't calm down", "hyperventilating"
                ],
                "severity_weight": 0.6,
                "requires_immediate": False
            },
            "substance_abuse": {
                "keywords": [
                    "drinking too much", "can't stop drinking", "using drugs",
                    "overdose", "addiction", "substance abuse", "drunk again",
                    "high again", "need a drink", "need drugs", "withdrawal"
                ],
                "severity_weight": 0.8,
                "requires_immediate": False
            },
            "domestic_violence": {
                "keywords": [
                    "hitting me", "abusing me", "domestic violence", "partner hurts me",
                    "afraid of him", "afraid of her", "threatens me", "controls me",
                    "won't let me leave", "isolates me", "bruises", "scared at home"
                ],
                "severity_weight": 0.9,
                "requires_immediate": True
            },
            "eating_disorder": {
                "keywords": [
                    "not eating", "starving myself", "binge eating", "purging",
                    "throwing up food", "laxatives", "diet pills", "too fat",
                    "hate my body", "food is enemy", "calories", "restrict eating"
                ],
                "severity_weight": 0.7,
                "requires_immediate": False
            }
        }
    
    async def detect_crisis(self, message: str, user_id: str) -> Dict[str, Any]:
        """Detect crisis indicators in a message."""
        try:
            # Detect crisis patterns
            pattern_scores = self._detect_crisis_patterns(message)
            
            # Get emotional analysis
            emotional_state = await self.emotional_intelligence.analyze_emotion(
                message, user_id
            )
            
            # Calculate overall crisis score
            crisis_score = self._calculate_crisis_score(
                pattern_scores, emotional_state, message
            )
            
            # Assess crisis level
            crisis_level, crisis_type = self._assess_crisis_level(
                crisis_score, pattern_scores
            )
            
            return {
                "crisis_detected": crisis_score > 0.3,
                "crisis_score": crisis_score,
                "crisis_level": crisis_level,
                "crisis_type": crisis_type,
                "detected_patterns": list(pattern_scores.keys()),
                "pattern_scores": pattern_scores,
                "emotional_state": emotional_state,
                "requires_immediate_action": crisis_score > 0.7,
                "timestamp": emotional_state.timestamp if emotional_state else None
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting crisis: {e}")
            return {
                "crisis_detected": False,
                "error": str(e)
            }
    
    def _detect_crisis_patterns(self, message: str) -> Dict[str, float]:
        """Detect crisis patterns in message."""
        message_lower = message.lower()
        detected_patterns = {}
        
        for pattern_name, pattern_data in self.crisis_patterns.items():
            keywords = pattern_data["keywords"]
            severity_weight = pattern_data["severity_weight"]
            
            # Count keyword matches
            matches = sum(1 for keyword in keywords if keyword in message_lower)
            
            if matches > 0:
                # Calculate pattern score based on matches and severity
                pattern_score = min((matches / len(keywords)) * severity_weight, 1.0)
                detected_patterns[pattern_name] = pattern_score
        
        return detected_patterns
    
    def _calculate_crisis_score(
        self,
        pattern_scores: Dict[str, float],
        emotional_state: Optional[EmotionalState],
        message: str
    ) -> float:
        """Calculate overall crisis score."""
        if not pattern_scores and not emotional_state:
            return 0.0
        
        # Base score from patterns
        pattern_score = max(pattern_scores.values()) if pattern_scores else 0.0
        
        # Emotional intensity factor
        emotional_factor = 0.0
        if emotional_state:
            # High intensity negative emotions increase crisis score
            negative_emotions = ["sadness", "fear", "anger", "anxiety"]
            if emotional_state.primary_emotion and emotional_state.primary_emotion.value in negative_emotions:
                emotional_factor = emotional_state.intensity * 0.3
        
        # Message urgency indicators
        urgency_keywords = ["help", "emergency", "urgent", "now", "immediately", "please"]
        urgency_factor = sum(0.1 for keyword in urgency_keywords if keyword in message.lower())
        urgency_factor = min(urgency_factor, 0.3)
        
        # Combine factors
        total_score = pattern_score + emotional_factor + urgency_factor
        
        return min(total_score, 1.0)
    
    def _assess_crisis_level(
        self,
        crisis_score: float,
        pattern_scores: Dict[str, float]
    ) -> Tuple[MentalHealthRisk, Optional[str]]:
        """Assess crisis level and type."""
        # Determine crisis type
        crisis_type = None
        if pattern_scores:
            # Get the pattern with highest score
            crisis_type = max(pattern_scores.items(), key=lambda x: x[1])[0]
        
        # Determine risk level
        if crisis_score >= 0.8:
            risk_level = MentalHealthRisk.CRITICAL
        elif crisis_score >= 0.6:
            risk_level = MentalHealthRisk.HIGH
        elif crisis_score >= 0.4:
            risk_level = MentalHealthRisk.MODERATE
        else:
            risk_level = MentalHealthRisk.LOW
        
        return risk_level, crisis_type
    
    def get_pattern_info(self, pattern_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific crisis pattern."""
        return self.crisis_patterns.get(pattern_name)
    
    def get_all_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Get all crisis patterns."""
        return self.crisis_patterns.copy()
    
    def add_custom_pattern(
        self,
        pattern_name: str,
        keywords: List[str],
        severity_weight: float,
        requires_immediate: bool = False
    ):
        """Add a custom crisis detection pattern."""
        self.crisis_patterns[pattern_name] = {
            "keywords": keywords,
            "severity_weight": severity_weight,
            "requires_immediate": requires_immediate
        }
        self.logger.info(f"Added custom crisis pattern: {pattern_name}")
    
    def update_pattern(
        self,
        pattern_name: str,
        keywords: Optional[List[str]] = None,
        severity_weight: Optional[float] = None,
        requires_immediate: Optional[bool] = None
    ):
        """Update an existing crisis pattern."""
        if pattern_name not in self.crisis_patterns:
            raise ValueError(f"Pattern {pattern_name} not found")
        
        pattern = self.crisis_patterns[pattern_name]
        
        if keywords is not None:
            pattern["keywords"] = keywords
        if severity_weight is not None:
            pattern["severity_weight"] = severity_weight
        if requires_immediate is not None:
            pattern["requires_immediate"] = requires_immediate
        
        self.logger.info(f"Updated crisis pattern: {pattern_name}")
    
    def remove_pattern(self, pattern_name: str):
        """Remove a crisis detection pattern."""
        if pattern_name in self.crisis_patterns:
            del self.crisis_patterns[pattern_name]
            self.logger.info(f"Removed crisis pattern: {pattern_name}")
        else:
            self.logger.warning(f"Pattern {pattern_name} not found for removal")
