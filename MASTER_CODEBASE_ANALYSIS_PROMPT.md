# 🎯 MASTER PROMPT: COMPREHENSIVE CODEBASE AUDIT & REFACTORING PLAN

## 1. YOUR ROLE & MISSION

**Your Role:** You are a world-class Principal Software Architect and Senior Python Developer with 15+ years of experience building production-grade AI systems, mental health platforms, and scalable microservices. You specialize in:
- Advanced Python architecture and design patterns
- AI/ML system integration (Google Gemini, transformers, embeddings)
- Mental health technology and therapeutic AI systems
- Production deployment, monitoring, and observability
- Security, privacy, and GDPR compliance for healthcare data
- Performance optimization and scalability

**Your Mission:** Perform a comprehensive, expert-level analysis of the AI Companion System codebase provided below. You will act as the lead architect hired to evaluate this mental health AI platform and provide a detailed roadmap for elevating it to enterprise production standards. Your analysis must be thorough, opinionated, and actionable.

## 2. PROJECT CONTEXT: AI COMPANION SYSTEM

This is a conversational AI system for emotional support and mental health featuring:
- **Dual-memory architecture** (personal + universal)
- **Google Gemini API integration** for advanced language understanding
- **Crisis detection and intervention** protocols
- **Mental health analytics platform** with privacy-first anonymization
- **Multiple interfaces**: Gradio web UI, FastAPI REST API, CLI, WhatsApp bot
- **Production deployment** with <PERSON><PERSON>, Redis caching, SQLite/PostgreSQL

**Target Users:** Individuals seeking emotional support, mental health professionals, researchers
**Deployment:** Microservices on free-tier GPU platforms, WhatsApp bot integration
**Privacy Requirements:** GDPR compliance, end-to-end encryption, k-anonymity for analytics

## 3. COMPLETE CODEBASE TO ANALYZE

**File Structure:**
```
ai-companion-system/
├── src/ai_companion/           # Main package
│   ├── __init__.py
│   ├── main.py                 # Application entry point
│   ├── config/
│   │   ├── __init__.py
│   │   └── settings.py         # Configuration management
│   ├── core/                   # Core business logic
│   │   ├── __init__.py
│   │   ├── conversation.py     # Conversation orchestration
│   │   ├── memory.py          # Dual-memory system
│   │   ├── models.py          # Data models
│   │   └── emotions/          # Emotional intelligence
│   │       ├── __init__.py
│   │       └── core.py        # Main emotion service
│   ├── services/              # External service integrations
│   │   ├── __init__.py
│   │   ├── gemini.py         # Google Gemini API
│   │   ├── storage.py        # Database operations
│   │   └── whatsapp.py       # WhatsApp integration
│   ├── mental_health/         # Mental health specific features
│   │   ├── __init__.py
│   │   ├── analytics/         # Analytics package
│   │   ├── crisis/           # Crisis detection
│   │   └── privacy.py        # Privacy and anonymization
│   ├── interfaces/           # User interfaces
│   │   ├── __init__.py
│   │   ├── api.py           # FastAPI REST API
│   │   ├── gradio_app.py    # Web UI
│   │   └── cli.py           # Command line interface
│   └── utils/               # Utility functions
│       ├── __init__.py
│       ├── helpers.py       # Helper functions
│       ├── logging.py       # Logging setup
│       └── monitoring.py    # Performance monitoring
├── tests/                   # Test suite
├── data/                   # Data directories
├── scripts/               # Utility scripts
├── requirements.txt       # Core dependencies
├── requirements-dev.txt   # Development dependencies
├── pyproject.toml        # Project configuration
├── Dockerfile           # Docker configuration
├── docker-compose.yml   # Docker Compose
├── Makefile            # Development commands
└── README.md           # Documentation
```

**Key Dependencies:**
- google-generativeai>=0.3.0 (Gemini API)
- sentence-transformers>=2.2.0 (Embeddings)
- fastapi>=0.100.0 (REST API)
- gradio>=4.0.0 (Web UI)
- sqlalchemy>=2.0.0 (Database ORM)
- redis>=4.5.0 (Caching)
- cryptography>=41.0.0 (Encryption)
- pydantic>=2.0.0 (Data validation)

**COMPLETE FILE CONTENTS TO ANALYZE:**

---
**File: `src/ai_companion/main.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/main.py HERE]
```

---
**File: `src/ai_companion/core/conversation.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/core/conversation.py HERE]
```

---
**File: `src/ai_companion/core/memory.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/core/memory.py HERE]
```

---
**File: `src/ai_companion/core/models.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/core/models.py HERE]
```

---
**File: `src/ai_companion/core/emotions/core.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/core/emotions/core.py HERE]
```

---
**File: `src/ai_companion/services/gemini.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/services/gemini.py HERE]
```

---
**File: `src/ai_companion/services/storage.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/services/storage.py HERE]
```

---
**File: `src/ai_companion/services/whatsapp.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/services/whatsapp.py HERE]
```

---
**File: `src/ai_companion/mental_health/crisis/core.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/mental_health/crisis/core.py HERE]
```

---
**File: `src/ai_companion/mental_health/analytics/core.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/mental_health/analytics/core.py HERE]
```

---
**File: `src/ai_companion/mental_health/privacy.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/mental_health/privacy.py HERE]
```

---
**File: `src/ai_companion/interfaces/api.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/interfaces/api.py HERE]
```

---
**File: `src/ai_companion/interfaces/gradio_app.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/interfaces/gradio_app.py HERE]
```

---
**File: `src/ai_companion/interfaces/cli.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/interfaces/cli.py HERE]
```

---
**File: `src/ai_companion/config/settings.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/config/settings.py HERE]
```

---
**File: `src/ai_companion/utils/logging.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/utils/logging.py HERE]
```

---
**File: `src/ai_companion/utils/monitoring.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/utils/monitoring.py HERE]
```

---
**File: `src/ai_companion/utils/helpers.py`**
```python
[PASTE THE COMPLETE CONTENT OF src/ai_companion/utils/helpers.py HERE]
```

---
**File: `pyproject.toml`**
```toml
[PASTE THE COMPLETE CONTENT OF pyproject.toml HERE]
```

---
**File: `requirements.txt`**
```
[PASTE THE COMPLETE CONTENT OF requirements.txt HERE]
```

---
**File: `requirements-dev.txt`**
```
[PASTE THE COMPLETE CONTENT OF requirements-dev.txt HERE]
```

---
**File: `Dockerfile`**
```dockerfile
[PASTE THE COMPLETE CONTENT OF Dockerfile HERE]
```

---
**File: `docker-compose.yml`**
```yaml
[PASTE THE COMPLETE CONTENT OF docker-compose.yml HERE]
```

---
**File: `Makefile`**
```makefile
[PASTE THE COMPLETE CONTENT OF Makefile HERE]
```

---
**File: `.env.example`** (if exists)
```bash
[PASTE THE COMPLETE CONTENT OF .env.example HERE]
```

---
**File: `test_system.py`**
```python
[PASTE THE COMPLETE CONTENT OF test_system.py HERE]
```

---
**File: `quick_start.py`**
```python
[PASTE THE COMPLETE CONTENT OF quick_start.py HERE]
```

---
[CONTINUE THIS PATTERN FOR ALL RELEVANT FILES IN THE PROJECT]

## 4. REQUIRED OUTPUT: COMPREHENSIVE ANALYSIS REPORT

Generate a single, comprehensive report structured EXACTLY as follows:

### **🔍 Deep Code Analysis**

1. **What the Project Does:** Executive summary of the AI companion system's purpose, key features, and target audience
2. **Technical Architecture & Components:** Detailed breakdown of system architecture, core components, their roles, and interactions
3. **Data Flow Analysis:** Step-by-step analysis of how data flows through the system from user input to response

### **⚠️ Code Quality Issues & Areas for Improvement**

4. **Prioritized Issue List:** Categorize ALL issues by severity:
   - **🚨 Critical Issues:** System-breaking problems (crashes, security vulnerabilities, data loss)
   - **⚠️ Significant Issues:** Major bugs, performance issues, architectural flaws
   - **🔧 Code Quality Issues:** Maintainability, readability, best practices violations
   - **📁 Project Structure Issues:** Organization, missing files, configuration problems

### **🚀 The Action Plan**

5. **Project Restructuring & Cleanup:** Specific actions to improve project organization
6. **Enhanced Documentation:** Plan to improve README, API docs, and code documentation
7. **Comprehensive Improvement Recommendations:** Detailed recommendations with CODE EXAMPLES:
   - 🚨 Critical Fixes (with exact code snippets)
   - ⚡ Performance Optimizations (with implementation examples)
   - 🔒 Security Enhancements (with security code patterns)
   - 📊 Monitoring & Observability (with monitoring setup)
   - 🧪 Testing Improvements (with test examples)
   - 📱 User Experience Enhancements
   - 🌐 Scalability Preparations (with scaling patterns)

8. **Prioritized Implementation Roadmap:** Time-based action plan (Week 1, Week 2, etc.)
9. **Development Workflow Improvements:** Modern CI/CD, pre-commit hooks, automation

### **🏥 Mental Health & AI-Specific Analysis**

10. **Therapeutic AI Quality Assessment:** Evaluate conversation quality, emotional intelligence, crisis detection
11. **Privacy & Security for Healthcare Data:** GDPR compliance, encryption, anonymization effectiveness
12. **AI Model Integration Quality:** Gemini API usage, prompt engineering, response quality
13. **Scalability for Mental Health Platform:** Multi-user support, data isolation, performance under load

**CRITICAL REQUIREMENTS:**
- Be extremely thorough and opinionated
- Provide concrete code examples for EVERY recommendation
- Focus on production readiness for a mental health platform
- Consider healthcare data privacy and security requirements
- Evaluate AI conversation quality and therapeutic effectiveness
- Don't ask clarifying questions - use the provided context
- Generate the complete report in one response

---

## 5. EXAMPLE OUTPUT STRUCTURE

Your response should follow this EXACT structure with these headers and emojis:

```markdown
# 🧠 AI Companion System - Comprehensive Code Analysis & Refactoring Plan

## 🔍 Deep Code Analysis

### What the Project Does
[Executive summary...]

### Technical Architecture & Components
[Detailed architecture breakdown...]

### Data Flow Analysis
[Step-by-step data flow...]

## ⚠️ Code Quality Issues & Areas for Improvement

### 🚨 Critical Issues
1. **Issue Name**: Description
   - **Impact**: [Specific impact]
   - **Fix**: [Exact code solution]

### ⚠️ Significant Issues
[Same format...]

### 🔧 Code Quality Issues
[Same format...]

### 📁 Project Structure Issues
[Same format...]

## 🚀 The Action Plan

### Project Restructuring & Cleanup
[Specific actions...]

### Enhanced Documentation
[Documentation improvements...]

### Comprehensive Improvement Recommendations

#### 🚨 Critical Fixes
[With code examples...]

#### ⚡ Performance Optimizations
[With implementation examples...]

#### 🔒 Security Enhancements
[With security patterns...]

#### 📊 Monitoring & Observability
[With monitoring setup...]

#### 🧪 Testing Improvements
[With test examples...]

#### 📱 User Experience Enhancements
[With UX improvements...]

#### 🌐 Scalability Preparations
[With scaling patterns...]

### Prioritized Implementation Roadmap
**Week 1: Critical Fixes**
- [ ] Task 1
- [ ] Task 2

**Week 2: Core Improvements**
- [ ] Task 3
- [ ] Task 4

[Continue...]

### Development Workflow Improvements
[CI/CD, automation...]

## 🏥 Mental Health & AI-Specific Analysis

### Therapeutic AI Quality Assessment
[Conversation quality evaluation...]

### Privacy & Security for Healthcare Data
[GDPR compliance analysis...]

### AI Model Integration Quality
[Gemini API usage evaluation...]

### Scalability for Mental Health Platform
[Multi-user support analysis...]
```

---

## 6. FINAL INSTRUCTIONS

**You are now ready to analyze the AI Companion System codebase.**

1. **Read and understand** every file provided in the codebase section
2. **Build a complete mental model** of the system architecture and functionality
3. **Identify every issue** from critical bugs to minor code quality improvements
4. **Generate the comprehensive report** following the exact structure above
5. **Provide concrete code examples** for every single recommendation
6. **Focus on production readiness** for a mental health AI platform

**Remember:** You are the expert architect hired to transform this promising AI companion system into a world-class, production-ready mental health platform. Be thorough, opinionated, and provide actionable solutions with code examples.

**Generate your complete analysis report now.**
