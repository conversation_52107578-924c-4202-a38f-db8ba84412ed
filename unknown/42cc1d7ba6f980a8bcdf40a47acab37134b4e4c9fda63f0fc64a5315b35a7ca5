# AI Companion System - Git Ignore File

# Environment variables and secrets
.env
.env.local
.env.production
.env.staging
.env.example.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/
.conda/

# IDE and Editors
.vscode/
.idea/
*.swp
*.swo
*~
.sublime-project
.sublime-workspace

# Jupyter Notebook
.ipynb_checkpoints

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/

# Type checking
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/

# Data directories (keep structure, ignore contents)
/data/db/*
!/data/db/.gitkeep
/data/logs/*
!/data/logs/.gitkeep
/data/cache/*
!/data/cache/.gitkeep

# Database files
*.db
*.sqlite
*.sqlite3

# Log files
*.log
logs/
ai_companion_performance.log
enhanced_ai_companion.log

# Cache files
.cache/
*.cache

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Model files (too large for git)
*.bin
*.safetensors
models/
checkpoints/

# API keys and sensitive data
api_keys.txt
secrets.txt
credentials.json
*.pem
*.key

# Backup files
*.bak
*.backup
backups/

# Docker
.dockerignore

# Redis
dump.rdb

# WhatsApp media
whatsapp_media/

# Performance and monitoring data
performance_data/
metrics/
monitoring/

# Documentation builds
docs/_build/
site/

# Package manager
node_modules/
package-lock.json
yarn.lock
