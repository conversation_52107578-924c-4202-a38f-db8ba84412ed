"""
Individual User Analytics for Mental Health Analysis.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import defaultdict, Counter

import numpy as np

from ...core.models import EmotionType, MemoryEntry, utc_now
from ...services.storage import StorageService
from .models import UserAnalytics, AnalyticsInsight


class IndividualAnalyzer:
    """Analyzer for individual user mental health patterns."""
    
    def __init__(self, storage_service: StorageService):
        """Initialize individual analyzer."""
        self.logger = logging.getLogger(__name__)
        self.storage_service = storage_service
    
    async def analyze_user(
        self,
        user_id: str,
        start_date: datetime,
        end_date: datetime,
        filters: Optional[Dict[str, Any]] = None
    ) -> UserAnalytics:
        """Analyze individual user patterns."""
        try:
            # Get user data
            memories = await self.storage_service.get_personal_memories(user_id, limit=1000)
            
            # Filter by date range
            filtered_memories = [
                memory for memory in memories
                if start_date <= memory.timestamp <= end_date
            ]
            
            if not filtered_memories:
                return self._create_empty_analytics(user_id)
            
            # Analyze different aspects
            emotional_patterns = self._analyze_emotional_patterns(filtered_memories)
            interaction_patterns = self._analyze_interaction_patterns(filtered_memories)
            progress_trends = self._analyze_progress_trends(filtered_memories)
            engagement_score = self._calculate_engagement_score(filtered_memories)
            consistency_score = self._calculate_consistency_score(filtered_memories)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                emotional_patterns, interaction_patterns, progress_trends
            )
            
            return UserAnalytics(
                user_id=user_id,
                emotional_patterns=emotional_patterns,
                interaction_patterns=interaction_patterns,
                progress_trends=progress_trends,
                engagement_score=engagement_score,
                consistency_score=consistency_score,
                recommendations=recommendations,
                timestamp=utc_now()
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing user {user_id}: {e}")
            return self._create_empty_analytics(user_id)
    
    def _create_empty_analytics(self, user_id: str) -> UserAnalytics:
        """Create empty analytics for users with no data."""
        return UserAnalytics(
            user_id=user_id,
            emotional_patterns={},
            interaction_patterns={},
            progress_trends={},
            engagement_score=0.0,
            consistency_score=0.0,
            recommendations=["Insufficient data for analysis"],
            timestamp=utc_now()
        )
    
    def _analyze_emotional_patterns(self, memories: List[MemoryEntry]) -> Dict[str, Any]:
        """Analyze emotional patterns in user memories."""
        try:
            emotions = [memory.emotion for memory in memories if memory.emotion]
            if not emotions:
                return {"error": "No emotional data available"}
            
            # Emotion distribution
            emotion_counts = Counter(emotions)
            total_emotions = len(emotions)
            emotion_distribution = {
                emotion.value: count / total_emotions
                for emotion, count in emotion_counts.items()
            }
            
            # Dominant emotions
            dominant_emotions = [
                emotion.value for emotion, _ in emotion_counts.most_common(3)
            ]
            
            # Emotional intensity over time
            intensity_data = []
            for memory in memories:
                if memory.emotion and hasattr(memory, 'emotional_intensity'):
                    intensity_data.append({
                        'timestamp': memory.timestamp,
                        'intensity': getattr(memory, 'emotional_intensity', 0.5)
                    })
            
            return {
                "emotion_distribution": emotion_distribution,
                "dominant_emotions": dominant_emotions,
                "total_emotional_events": total_emotions,
                "intensity_timeline": intensity_data,
                "analysis_period": {
                    "start": min(memory.timestamp for memory in memories),
                    "end": max(memory.timestamp for memory in memories)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing emotional patterns: {e}")
            return {"error": str(e)}
    
    def _analyze_interaction_patterns(self, memories: List[MemoryEntry]) -> Dict[str, Any]:
        """Analyze interaction patterns."""
        try:
            # Interaction frequency by type
            interaction_types = [memory.interaction_type for memory in memories]
            type_counts = Counter(interaction_types)
            
            # Daily interaction patterns
            daily_interactions = defaultdict(int)
            for memory in memories:
                day = memory.timestamp.date()
                daily_interactions[day] += 1
            
            # Average interactions per day
            if daily_interactions:
                avg_daily = sum(daily_interactions.values()) / len(daily_interactions)
            else:
                avg_daily = 0
            
            return {
                "interaction_type_distribution": {
                    itype.value: count for itype, count in type_counts.items()
                },
                "total_interactions": len(memories),
                "average_daily_interactions": avg_daily,
                "most_active_days": dict(
                    sorted(daily_interactions.items(), key=lambda x: x[1], reverse=True)[:7]
                )
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing interaction patterns: {e}")
            return {"error": str(e)}
    
    def _analyze_progress_trends(self, memories: List[MemoryEntry]) -> Dict[str, Any]:
        """Analyze progress and improvement trends."""
        try:
            # Sort memories by timestamp
            sorted_memories = sorted(memories, key=lambda m: m.timestamp)
            
            if len(sorted_memories) < 3:
                return {"error": "Insufficient data for trend analysis"}
            
            # Analyze emotional improvement over time
            emotional_scores = []
            for memory in sorted_memories:
                if memory.emotion:
                    # Simple scoring: positive emotions = higher score
                    positive_emotions = {EmotionType.JOY, EmotionType.EXCITEMENT, EmotionType.HOPE, EmotionType.LOVE, EmotionType.PRIDE}
                    score = 1.0 if memory.emotion in positive_emotions else 0.0
                    emotional_scores.append(score)
            
            if len(emotional_scores) >= 3:
                trend = self._calculate_trend(emotional_scores)
            else:
                trend = "insufficient_data"
            
            return {
                "emotional_trend": trend,
                "trend_confidence": min(len(emotional_scores) / 10, 1.0),
                "data_points": len(emotional_scores),
                "analysis_period_days": (sorted_memories[-1].timestamp - sorted_memories[0].timestamp).days
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing progress trends: {e}")
            return {"error": str(e)}
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction for a series of values."""
        if len(values) < 3:
            return "insufficient_data"
        
        # Simple linear trend calculation
        x = list(range(len(values)))
        correlation = np.corrcoef(x, values)[0, 1]
        
        if correlation > 0.3:
            return "improving"
        elif correlation < -0.3:
            return "declining"
        else:
            return "stable"
    
    def _calculate_engagement_score(self, memories: List[MemoryEntry]) -> float:
        """Calculate user engagement score."""
        try:
            if not memories:
                return 0.0
            
            # Factors for engagement
            total_interactions = len(memories)
            time_span = (max(memory.timestamp for memory in memories) - 
                        min(memory.timestamp for memory in memories)).days
            
            if time_span == 0:
                time_span = 1
            
            # Engagement = interactions per day, normalized
            daily_rate = total_interactions / time_span
            engagement_score = min(daily_rate / 5.0, 1.0)  # Normalize to 0-1
            
            return round(engagement_score, 3)
            
        except Exception as e:
            self.logger.error(f"Error calculating engagement score: {e}")
            return 0.0
    
    def _calculate_consistency_score(self, memories: List[MemoryEntry]) -> float:
        """Calculate consistency of interactions over time."""
        try:
            if len(memories) < 7:
                return 0.0
            
            # Group by day
            daily_counts = defaultdict(int)
            for memory in memories:
                day = memory.timestamp.date()
                daily_counts[day] += 1
            
            # Calculate coefficient of variation (lower = more consistent)
            counts = list(daily_counts.values())
            if len(counts) < 2:
                return 0.0
            
            mean_count = np.mean(counts)
            std_count = np.std(counts)
            
            if mean_count == 0:
                return 0.0
            
            cv = std_count / mean_count
            consistency_score = max(0, 1 - cv)  # Invert so higher = more consistent
            
            return round(consistency_score, 3)
            
        except Exception as e:
            self.logger.error(f"Error calculating consistency score: {e}")
            return 0.0
    
    def _generate_recommendations(
        self,
        emotional_patterns: Dict[str, Any],
        interaction_patterns: Dict[str, Any],
        progress_trends: Dict[str, Any]
    ) -> List[str]:
        """Generate personalized recommendations."""
        recommendations = []
        
        # Emotional pattern recommendations
        if "dominant_emotions" in emotional_patterns:
            dominant = emotional_patterns["dominant_emotions"]
            if "sadness" in dominant or "anxiety" in dominant:
                recommendations.append("Consider mindfulness exercises to help manage difficult emotions")
            if "anger" in dominant or "frustration" in dominant:
                recommendations.append("Practice deep breathing techniques when feeling overwhelmed")
        
        # Interaction pattern recommendations
        if "average_daily_interactions" in interaction_patterns:
            avg_daily = interaction_patterns["average_daily_interactions"]
            if avg_daily < 1:
                recommendations.append("Try to engage more regularly for better support")
            elif avg_daily > 10:
                recommendations.append("Consider taking breaks to avoid emotional fatigue")
        
        # Progress trend recommendations
        if "emotional_trend" in progress_trends:
            trend = progress_trends["emotional_trend"]
            if trend == "declining":
                recommendations.append("Your emotional patterns suggest you might benefit from additional support")
            elif trend == "improving":
                recommendations.append("Great progress! Keep up the positive momentum")
        
        if not recommendations:
            recommendations.append("Continue your current approach - you're doing well")
        
        return recommendations
