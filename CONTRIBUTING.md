# Contributing to AI Companion System

Thank you for your interest in contributing to the AI Companion System! This document provides guidelines for contributing to this mental health support platform.

## 🤝 Code of Conduct

This project is committed to providing a welcoming and inclusive environment for all contributors. We expect all participants to:

- Be respectful and considerate in all interactions
- Focus on constructive feedback and collaboration
- Respect the sensitive nature of mental health topics
- Maintain confidentiality regarding any user data or scenarios

## 🚀 Getting Started

### Prerequisites

- Python 3.9 or higher
- Git
- Google Gemini API key
- Basic understanding of mental health ethics

### Development Setup

1. **Fork and clone the repository:**
   ```bash
   git clone https://github.com/your-username/ai-companion-system.git
   cd ai-companion-system
   ```

2. **Set up development environment:**
   ```bash
   make setup
   # or manually:
   python -m venv venv
   source venv/bin/activate  # Windows: venv\Scripts\activate
   pip install -r requirements-dev.txt
   pip install -e .
   ```

3. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and settings
   ```

4. **Run tests to verify setup:**
   ```bash
   make test
   python test_system.py
   ```

## 📝 Development Guidelines

### Code Standards

- **Python Style**: Follow PEP 8, use Black for formatting
- **Type Hints**: All functions must have type annotations
- **Docstrings**: Use Google-style docstrings for all public methods
- **Testing**: Write tests for all new features
- **Error Handling**: Implement comprehensive error handling

### Code Quality Tools

```bash
# Format code
make format

# Run linting
make lint

# Run all quality checks
make check
```

### Mental Health Considerations

When working on this project, please:

- **Prioritize User Safety**: Crisis detection and intervention features are critical
- **Respect Privacy**: Handle all user data with extreme care
- **Follow Therapeutic Guidelines**: Ensure responses align with established therapeutic practices
- **Avoid Medical Claims**: The system provides support, not medical diagnosis
- **Cultural Sensitivity**: Consider diverse backgrounds and experiences

## 🔧 Development Workflow

### Branch Naming

- `feature/description` - New features
- `fix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring

### Commit Messages

Use conventional commit format:
```
type(scope): description

feat(crisis): add new crisis detection patterns
fix(memory): resolve memory leak in cache
docs(readme): update installation instructions
```

### Pull Request Process

1. **Create Feature Branch:**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes:**
   - Write code following our standards
   - Add/update tests
   - Update documentation if needed

3. **Test Your Changes:**
   ```bash
   make test
   make lint
   python test_system.py
   ```

4. **Submit Pull Request:**
   - Provide clear description of changes
   - Reference any related issues
   - Include screenshots for UI changes
   - Ensure all checks pass

## 🧪 Testing

### Test Categories

- **Unit Tests**: Test individual components
- **Integration Tests**: Test component interactions
- **Performance Tests**: Validate response times and resource usage
- **System Tests**: End-to-end validation

### Running Tests

```bash
# All tests
make test

# Specific test categories
make test-unit
make test-integration
make test-performance

# With coverage
make test-coverage
```

### Writing Tests

- Place tests in appropriate directories under `tests/`
- Use descriptive test names
- Test both success and failure scenarios
- Mock external dependencies
- Include performance benchmarks for critical paths

## 📚 Documentation

### Documentation Standards

- Keep README.md up to date
- Document all configuration options
- Provide clear setup instructions
- Include usage examples
- Document API endpoints

### Documentation Updates

When making changes that affect:
- Installation process
- Configuration options
- API endpoints
- User interfaces
- Mental health features

Please update the relevant documentation.

## 🐛 Bug Reports

### Before Reporting

1. Check existing issues
2. Verify the bug with latest version
3. Test with minimal configuration

### Bug Report Template

```markdown
**Bug Description**
Clear description of the issue

**Steps to Reproduce**
1. Step one
2. Step two
3. Step three

**Expected Behavior**
What should happen

**Actual Behavior**
What actually happens

**Environment**
- OS: [e.g., Ubuntu 20.04]
- Python version: [e.g., 3.11.0]
- Package version: [e.g., 1.0.0]

**Additional Context**
Any other relevant information
```

## 💡 Feature Requests

### Feature Request Guidelines

- Ensure the feature aligns with mental health support goals
- Consider privacy and security implications
- Provide clear use cases and benefits
- Consider implementation complexity

### Feature Request Template

```markdown
**Feature Description**
Clear description of the proposed feature

**Use Case**
Why is this feature needed?

**Proposed Solution**
How should this feature work?

**Alternatives Considered**
Other approaches you've considered

**Mental Health Impact**
How does this improve user support?
```

## 🔒 Security

### Security Considerations

- Never commit API keys or sensitive data
- Follow secure coding practices
- Report security vulnerabilities privately
- Consider privacy implications of all changes

### Reporting Security Issues

For security vulnerabilities, please email: <EMAIL>

Do not create public issues for security problems.

## 📞 Getting Help

- **Documentation**: Check the README and wiki
- **Issues**: Search existing issues
- **Discussions**: Use GitHub Discussions for questions
- **Email**: <EMAIL>

## 🎯 Areas for Contribution

We especially welcome contributions in:

- **Crisis Detection**: Improving pattern recognition
- **Therapeutic Techniques**: Adding new intervention methods
- **Privacy Features**: Enhancing data protection
- **Performance**: Optimizing response times
- **Testing**: Expanding test coverage
- **Documentation**: Improving user guides
- **Accessibility**: Making the system more inclusive

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to mental health support technology! 🧠💙
