# 🧠 AI Companion System

A production-ready conversational AI system for emotional support and mental health, featuring advanced emotional intelligence, dual-memory architecture, crisis detection, and therapeutic conversation capabilities.

[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

---

## 📋 **What This Project Does**

The AI Companion System is a comprehensive mental health support platform that provides:

### 🎯 **Core Features**
- **🤖 Conversational AI**: Advanced emotional support using Google Gemini AI
- **🧠 Emotional Intelligence**: Real-time emotion detection and empathetic responses
- **🚨 Crisis Detection**: Automated risk assessment with intervention protocols
- **💾 Dual-Memory Architecture**: Personal (user-specific) + Universal (shared) memory systems
- **🏥 Mental Health Analytics**: Privacy-first data insights for research and therapy
- **🌐 Multiple Interfaces**: Web UI, REST API, CLI, and WhatsApp bot integration
- **🔒 Privacy-First Design**: End-to-end encryption and data anonymization

### 🎭 **Therapeutic Capabilities**
- **Validation & Active Listening**: Acknowledges and validates user emotions
- **Cognitive Reframing**: Helps users reframe negative thought patterns
- **Mindfulness & Breathing**: Guided exercises for anxiety and stress
- **Safety Planning**: Crisis intervention and resource referrals
- **Progress Tracking**: Long-term emotional pattern analysis

---

## 🏗️ **Technical Architecture**

### **System Components**

```mermaid
graph TD
    A[User Input] --> B[Interface Layer]
    B --> C[ConversationService]
    C --> D[EmotionalIntelligence]
    C --> E[MemoryService]
    C --> F[GeminiService]
    D --> G[CrisisDetection]
    E --> H[StorageService]
    F --> I[AI Response]
    G --> J[Intervention]
    I --> K[User Output]
    J --> K
```

### **Core Services**

| Service | Responsibility | Key Features |
|---------|---------------|--------------|
| **ConversationService** | Orchestrates conversation flow | Context management, response generation |
| **EmotionalIntelligenceService** | Emotion analysis and therapeutic responses | Pattern matching + AI analysis |
| **MemoryService** | Dual-memory architecture | Personal/universal memory with importance scoring |
| **GeminiService** | Google Gemini AI integration | Advanced language understanding and generation |
| **CrisisDetectionService** | Risk assessment and intervention | Pattern-based crisis detection with escalation |
| **MentalHealthAnalytics** | Privacy-first analytics | Anonymized insights and population trends |
| **StorageService** | Data persistence | SQLite + Redis caching with encryption |

### **Data Flow**

1. **Input Processing**: User message received via interface
2. **Emotion Analysis**: Pattern matching + AI-powered emotion detection
3. **Memory Retrieval**: Relevant personal/universal memories retrieved
4. **AI Processing**: Gemini generates contextual, empathetic response
5. **Crisis Assessment**: Risk evaluation and intervention triggering
6. **Response Delivery**: Therapeutic response with resources if needed
7. **Memory Storage**: Conversation stored with emotional metadata
8. **Analytics**: Anonymized data aggregated for insights

---

## 🚀 **Quick Start Guide**

### **Prerequisites**

- **Python 3.9+** (3.11 recommended)
- **Google Gemini API Key** ([Get one here](https://makersuite.google.com/app/apikey))
- **Git**
- **4GB+ RAM** recommended
- **Redis** (optional, for caching)

### **Installation**

#### **Option 1: Quick Start (Easiest)**

```bash
# Clone the repository
git clone https://github.com/your-username/ai-companion-system.git
cd ai-companion-system

# Run the quick start script (handles everything automatically)
python quick_start.py
```

#### **Option 2: Using pip (Recommended)**

```bash
# Clone the repository
git clone https://github.com/your-username/ai-companion-system.git
cd ai-companion-system

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# For development (includes testing and code quality tools)
pip install -r requirements-dev.txt

# Optional: Install as editable package
pip install -e .
```

#### **Option 2: Using Docker**

```bash
# Clone and run with Docker
git clone https://github.com/your-username/ai-companion-system.git
cd ai-companion-system

# Build and run
docker-compose up -d

# Or build manually
docker build -t ai-companion .
docker run -p 7860:7860 -p 8000:8000 --env-file .env ai-companion
```

#### **Option 3: Development Setup (Automated)**

```bash
# Clone repository
git clone https://github.com/your-username/ai-companion-system.git
cd ai-companion-system

# Run automated setup (creates venv, installs deps, sets up .env)
make setup

# Or use the setup script
python scripts/setup.py
```

### **Configuration**

1. **Copy environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit `.env` file with your settings:**
   ```bash
   # Required: Add your Gemini API key
   GEMINI_API_KEY=your_actual_api_key_here

   # Optional: Customize other settings
   ENVIRONMENT=development
   DEBUG_MODE=true
   GRADIO_PORT=7860
   API_PORT=8000

   # For production, also set:
   SECRET_KEY=your_secure_32_character_secret_key
   ```

3. **Create data directories:**
   ```bash
   mkdir -p data/{db,logs,cache}
   ```

### **Running the System**

#### **Web Interface (Gradio)**
```bash
# Using make
make run

# Or directly
python -m ai_companion.main

# Access at: http://localhost:7860
```

#### **API Server**
```bash
# Using make
make run-api

# Or directly
uvicorn ai_companion.interfaces.api:app --host 0.0.0.0 --port 8000

# API docs at: http://localhost:8000/docs
```

#### **Command Line Interface**
```bash
python -m ai_companion.interfaces.cli
```

---

## 💡 **Usage Examples**

### **Web Interface**

1. Open http://localhost:7860 in your browser
2. Enter a message like "I'm feeling anxious about work"
3. Receive empathetic response with emotional analysis
4. View conversation history and emotional patterns

### **REST API**

```bash
# Send a message
curl -X POST "http://localhost:8000/api/v1/conversation/message" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "message": "I am feeling overwhelmed with everything",
    "interaction_type": "conversation"
  }'

# Get user analytics
curl "http://localhost:8000/api/v1/analytics/user/user123"

# Health check
curl "http://localhost:8000/health"
```

### **Python API**

```python
import asyncio
from ai_companion import AICompanionSystem

async def example():
    # Initialize system
    system = AICompanionSystem()
    await system.initialize()

    # Send message
    response = await system.conversation_service.process_message(
        user_id="user123",
        message="I'm having a difficult day",
        interaction_type="conversation"
    )

    print(f"Response: {response['response']}")
    print(f"Emotion detected: {response['emotion_analysis']}")

# Run example
asyncio.run(example())
```

### **WhatsApp Integration**

```python
# Configure WhatsApp in .env
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_ACCESS_TOKEN=your_access_token
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_verify_token

# Run WhatsApp service
python -m ai_companion.services.whatsapp
```

---

## 🧪 **Testing**

### **Run All Tests**
```bash
# Using make
make test

# Or directly
pytest tests/ -v
```

### **Test Categories**
```bash
# Unit tests
make test-unit
pytest tests/unit/ -v

# Integration tests
make test-integration
pytest tests/integration/ -v

# Performance tests
make test-performance
pytest tests/performance/ -v

# Coverage report
make test-coverage
pytest --cov=ai_companion --cov-report=html
```

### **System Validation**
```bash
# Run comprehensive system validation
make test-system
python test_system.py

# Run health check
make health-check
python scripts/health_check.py

# Validate configuration
python -c "from ai_companion.config.settings import validate_settings; validate_settings(); print('✅ Configuration valid')"
```

---

## 🔧 **Development**

### **Code Quality**

```bash
# Format code
make format
black src/ai_companion/ tests/
isort src/ai_companion/ tests/

# Lint code
make lint
flake8 src/ai_companion/ tests/
mypy src/ai_companion/

# Run all quality checks
make check
```

### **Project Structure**

```
ai-companion-system/
├── src/ai_companion/           # Main package
│   ├── core/                   # Core business logic
│   │   ├── conversation.py     # Conversation orchestration
│   │   ├── memory.py          # Dual-memory system
│   │   ├── emotions/          # Emotional intelligence
│   │   │   ├── core.py        # Main emotion service
│   │   │   └── patterns.py    # Pattern matching
│   │   └── models.py          # Data models
│   ├── services/              # External service integrations
│   │   ├── gemini.py         # Google Gemini API
│   │   ├── storage.py        # Database operations
│   │   └── whatsapp.py       # WhatsApp integration
│   ├── mental_health/         # Mental health specific features
│   │   ├── analytics/         # Analytics package
│   │   │   ├── core.py        # Main analytics service
│   │   │   ├── individual.py  # Individual analytics
│   │   │   └── population.py  # Population analytics
│   │   ├── crisis/           # Crisis detection
│   │   │   ├── core.py        # Main crisis service
│   │   │   ├── detector.py    # Crisis detection logic
│   │   │   └── intervention.py # Intervention management
│   │   └── privacy.py        # Privacy and anonymization
│   ├── interfaces/           # User interfaces
│   │   ├── api.py           # FastAPI REST API
│   │   ├── gradio_app.py    # Web UI
│   │   └── cli.py           # Command line interface
│   ├── config/              # Configuration management
│   │   └── settings.py      # Settings and validation
│   ├── utils/               # Utility functions
│   │   ├── helpers.py       # Helper functions
│   │   ├── logging.py       # Logging setup
│   │   └── monitoring.py    # Performance monitoring
│   └── main.py             # Application entry point
├── tests/                   # Test suite
│   ├── unit/               # Unit tests
│   ├── integration/        # Integration tests
│   └── performance/        # Performance tests
├── data/                   # Data directories
│   ├── db/                # Database files (.gitkeep only)
│   ├── logs/              # Log files (.gitkeep only)
│   └── cache/             # Cache files (.gitkeep only)
├── scripts/               # Utility scripts
│   ├── setup.py          # Automated setup script
│   ├── dev_setup.py      # Development setup
│   └── production_setup.py # Production setup
├── requirements.txt       # Core dependencies
├── requirements-dev.txt   # Development dependencies
├── pyproject.toml        # Project configuration
├── .env.example          # Environment template
├── .gitignore           # Git ignore rules
├── Dockerfile           # Docker configuration
├── docker-compose.yml   # Docker Compose
├── Makefile            # Development commands
└── README.md           # This file
```

---

## � **Recent Improvements & Code Quality**

### **✅ Critical Fixes Applied**
- **Fixed Import Path Issues**: Corrected inconsistent import paths in Makefile, Docker, and scripts
- **Resolved Docker Configuration**: Fixed CMD paths in both production and development Dockerfiles
- **Updated Docker Compose**: Corrected service commands for proper container execution
- **Added Missing Files**: Created `test_system.py` for comprehensive system validation
- **Enhanced Package Exports**: Updated `__init__.py` files for proper module imports
- **Cleaned Project Structure**: Removed redundant directories and build artifacts

### **🏗️ Architecture Improvements**
- **Modular Design**: Well-organized package structure with clear separation of concerns
- **Dependency Injection**: Proper service initialization and dependency management
- **Error Handling**: Comprehensive error handling with specific exception types
- **Performance Monitoring**: Built-in performance tracking and metrics collection
- **Configuration Management**: Centralized settings with validation and environment-specific configs

### **🛡️ Security Enhancements**
- **Environment Protection**: Comprehensive `.env.example` with security guidelines
- **Input Validation**: Proper validation for all user inputs and API endpoints
- **Secret Management**: Secure handling of API keys and sensitive configuration
- **Rate Limiting**: Built-in rate limiting to prevent abuse
- **Data Encryption**: Encryption for sensitive data storage

### **📋 Best Practices Implemented**
- **Type Hints**: Full type annotation throughout the codebase
- **Documentation**: Comprehensive docstrings and inline documentation
- **Testing Structure**: Organized test suite with unit, integration, and performance tests
- **Code Formatting**: Black, isort, and flake8 configuration for consistent code style
- **Git Workflow**: Proper `.gitignore` and directory structure for version control

### **🚀 Production Readiness**
- **Docker Support**: Both production and development Docker configurations
- **Health Checks**: Comprehensive health monitoring and validation
- **Monitoring**: Built-in performance metrics and system monitoring
- **Scalability**: Redis caching and database optimization
- **CI/CD Ready**: Makefile with all necessary development and deployment commands

---

## �🔒 **Privacy & Security**

### **Data Protection**
- **End-to-End Encryption**: All personal data encrypted at rest
- **Data Anonymization**: Population analytics use k-anonymity (k≥5)
- **Minimal Data Collection**: Only essential data for functionality
- **User Control**: Users can delete their data anytime
- **GDPR Compliance**: Built with privacy regulations in mind

### **Security Features**
- **API Rate Limiting**: Prevents abuse and ensures fair usage
- **Input Validation**: All inputs sanitized and validated
- **Secure Configuration**: Environment-based secrets management
- **Crisis Escalation**: Automatic escalation for high-risk situations
- **Audit Logging**: Comprehensive logging for security monitoring

### **Mental Health Ethics**
- **Professional Guidelines**: Follows established therapeutic practices
- **Crisis Resources**: Immediate access to professional help
- **Transparency**: Clear about AI limitations and when to seek human help
- **Non-Diagnostic**: Provides support, not medical diagnosis
- **Cultural Sensitivity**: Designed to respect diverse backgrounds

---

## 📊 **Configuration Reference**

### **Environment Variables**

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `GEMINI_API_KEY` | Google Gemini API key | - | ✅ |
| `ENVIRONMENT` | Environment (development/production) | development | ❌ |
| `DEBUG_MODE` | Enable debug logging | true | ❌ |
| `GRADIO_PORT` | Web UI port | 7860 | ❌ |
| `API_PORT` | API server port | 8000 | ❌ |
| `DATABASE_PATH` | SQLite database path | data/db/ai_companion.db | ❌ |
| `REDIS_ENABLED` | Enable Redis caching | false | ❌ |
| `CRISIS_THRESHOLD` | Crisis detection threshold | 0.7 | ❌ |
| `MIN_COHORT_SIZE` | Minimum users for analytics | 10 | ❌ |

### **Advanced Configuration**

```bash
# Mental Health Settings
CRISIS_THRESHOLD=0.7                    # Crisis detection sensitivity
MODERATE_RISK_THRESHOLD=0.4             # Moderate risk threshold
ENABLE_MENTAL_HEALTH_PLATFORM=true     # Enable analytics platform
ANONYMIZATION_ENABLED=true             # Enable data anonymization
K_ANONYMITY_LEVEL=5                    # K-anonymity level

# Performance Settings
CACHE_SIZE=1000                        # Response cache size
THREAD_POOL_SIZE=4                     # Thread pool for async operations
MEMORY_THRESHOLD_MB=500                # Memory usage threshold
TARGET_RESPONSE_TIME=0.3               # Target response time (seconds)

# Gemini Model Settings
GEMINI_MODEL=gemini-1.5-flash          # Gemini model to use
GEMINI_TEMPERATURE=0.7                 # Response creativity (0-2)
GEMINI_TOP_P=0.9                       # Nucleus sampling parameter
GEMINI_TOP_K=40                        # Top-k sampling parameter
GEMINI_MAX_TOKENS=2048                 # Maximum response length
```

---

## 🚨 **Crisis Detection & Intervention**

### **Crisis Types Detected**
- **Suicidal Ideation**: Thoughts of self-harm or suicide
- **Self-Harm**: Deliberate self-injury behaviors
- **Severe Depression**: Major depressive episodes
- **Panic Attacks**: Acute anxiety episodes
- **Substance Abuse**: Drug or alcohol abuse patterns
- **Domestic Violence**: Abuse or violence situations
- **Eating Disorders**: Disordered eating behaviors

### **Intervention Protocols**
1. **Immediate Assessment**: Real-time risk scoring
2. **Resource Provision**: Crisis hotlines and support services
3. **Safety Planning**: Collaborative safety strategies
4. **Follow-up Scheduling**: Automated check-ins
5. **Professional Referral**: Connection to human therapists

### **Crisis Resources**
- **National Suicide Prevention Lifeline**: 988
- **Crisis Text Line**: Text HOME to 741741
- **National Domestic Violence Hotline**: 1-800-799-7233
- **SAMHSA National Helpline**: 1-************

---

## 📈 **Analytics & Insights**

### **Individual Analytics**
- **Emotional Patterns**: Dominant emotions and trends
- **Interaction Patterns**: Usage frequency and timing
- **Progress Tracking**: Improvement over time
- **Engagement Metrics**: Platform usage statistics
- **Personalized Recommendations**: Tailored suggestions

### **Population Analytics** (Anonymized)
- **Emotion Trends**: Population-level emotional patterns
- **Risk Assessment**: Aggregate mental health indicators
- **Intervention Effectiveness**: Success rates and outcomes
- **Research Insights**: De-identified data for research

### **Privacy Protection**
- **K-Anonymity**: Minimum group size of 5 for all analytics
- **Data Aggregation**: Individual data never exposed
- **Differential Privacy**: Mathematical privacy guarantees
- **Opt-out Options**: Users can disable analytics participation

---

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **Installation Problems**
```bash
# Python version issues
python --version  # Should be 3.9+
pip install --upgrade pip

# Virtual environment issues
python -m venv venv --clear
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

# Install as editable package
pip install -e .

# Permission errors (Linux/Mac)
sudo chown -R $USER:$USER data/
chmod -R 755 data/
```

#### **API Key Issues**
```bash
# Verify API key is set
echo $GEMINI_API_KEY

# Test API key
curl -H "Authorization: Bearer $GEMINI_API_KEY" \
  https://generativelanguage.googleapis.com/v1/models

# Check .env file
cat .env | grep GEMINI_API_KEY
```

#### **Import and Module Issues**
```bash
# If you get import errors, ensure proper installation
pip install -e .

# Check if package is properly installed
python -c "import ai_companion; print('✅ Package imported successfully')"

# Verify PYTHONPATH (if needed)
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
```

#### **Database Issues**
```bash
# Reset database
rm data/db/*.db
python -c "from ai_companion.services.storage import StorageService; StorageService().initialize_database()"

# Check database permissions
ls -la data/db/

# Create data directories if missing
mkdir -p data/{db,logs,cache}
```

#### **Memory Issues**
```bash
# Check memory usage
python -c "import psutil; print(f'Memory: {psutil.virtual_memory().percent}%')"

# Reduce cache size in .env
CACHE_SIZE=100
MEMORY_THRESHOLD_MB=200
```

### **Performance Optimization**

#### **Enable Redis Caching**
```bash
# Install Redis
sudo apt-get install redis-server  # Ubuntu
brew install redis                  # macOS

# Enable in .env
REDIS_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379

# Test Redis connection
redis-cli ping
```

#### **Optimize Gemini Usage**
```bash
# Reduce token usage
GEMINI_MAX_TOKENS=1024
GEMINI_TEMPERATURE=0.5

# Use faster model
GEMINI_MODEL=gemini-1.5-flash
```

### **Debugging**

#### **Enable Debug Mode**
```bash
# In .env file
DEBUG_MODE=true
LOG_LEVEL=DEBUG

# View logs
tail -f data/logs/ai_companion.log
```

#### **Test Individual Components**
```python
# Test Gemini connection
from ai_companion.services.gemini import GeminiService
service = GeminiService()
response = await service.generate_response("Hello")

# Test emotion detection
from ai_companion.core.emotions import EmotionalIntelligenceService
emotions = EmotionalIntelligenceService(service)
result = await emotions.analyze_emotion("I'm feeling sad")

# Test crisis detection
from ai_companion.mental_health.crisis import CrisisDetectionService
crisis = CrisisDetectionService(emotions)
analysis = await crisis.analyze_message("I want to hurt myself", "test_user")
```

---

## 🤝 **Contributing**

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### **Development Workflow**
1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Make** your changes
4. **Test** your changes (`make test`)
5. **Format** your code (`make format`)
6. **Commit** your changes (`git commit -m 'Add amazing feature'`)
7. **Push** to the branch (`git push origin feature/amazing-feature`)
8. **Open** a Pull Request

### **Code Standards**
- **Python 3.9+** compatibility
- **Type hints** for all functions
- **Docstrings** for all public methods
- **Unit tests** for new features
- **Black** code formatting
- **Flake8** linting compliance

---

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 🙏 **Acknowledgments**

- **Google Gemini AI** for advanced language capabilities
- **Mental Health Community** for guidance on therapeutic practices
- **Open Source Contributors** for various libraries and tools
- **Privacy Advocates** for security and privacy best practices

---

## 📞 **Support & Contact**

- **Documentation**: [GitHub Wiki](https://github.com/your-username/ai-companion-system/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-username/ai-companion-system/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/ai-companion-system/discussions)
- **Email**: <EMAIL>

---

## ⚠️ **Important Disclaimers**

- **Not a Replacement for Professional Help**: This AI companion provides support but is not a substitute for professional mental health treatment
- **Emergency Situations**: In crisis situations, contact emergency services (911) or crisis hotlines immediately
- **Data Privacy**: While we prioritize privacy, users should be aware that conversations are processed by AI systems
- **Accuracy**: AI responses are generated and may not always be accurate or appropriate
- **Medical Advice**: This system does not provide medical diagnosis or treatment recommendations

---

*Built with ❤️ for human emotional wellbeing and mental health support.*