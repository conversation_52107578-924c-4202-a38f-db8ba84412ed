"""
Logging configuration for the AI Companion System.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

from ..config.settings import settings


def setup_logging(
    log_level: Optional[str] = None,
    log_file: Optional[str] = None,
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> logging.Logger:
    """
    Setup logging configuration for the AI Companion System.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file (defaults to data/logs/ai_companion.log)
        max_bytes: Maximum size of log file before rotation
        backup_count: Number of backup log files to keep
    
    Returns:
        Configured logger instance
    """
    # Use settings if not provided
    log_level = log_level or settings.log_level
    
    # Create logs directory if it doesn't exist
    log_dir = Path("data/logs")
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Default log file
    if not log_file:
        log_file = log_dir / "ai_companion.log"
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, log_level.upper()))
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        filename=log_file,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, log_level.upper()))
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # Create logger for this module
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured - Level: {log_level}, File: {log_file}")
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance for a specific module.
    
    Args:
        name: Logger name (usually __name__)
    
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


class PerformanceLogger:
    """Logger for performance metrics and timing."""
    
    def __init__(self, name: str = "performance"):
        self.logger = logging.getLogger(name)
        self.performance_file = Path("data/logs/performance.log")
        self.performance_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Create performance-specific handler
        if not self.logger.handlers:
            handler = logging.FileHandler(self.performance_file)
            formatter = logging.Formatter(
                '%(asctime)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def log_response_time(self, user_id: str, response_time: float, message_length: int):
        """Log response time metrics."""
        self.logger.info(f"RESPONSE_TIME,{user_id},{response_time:.3f},{message_length}")
    
    def log_memory_usage(self, user_id: str, memory_count: int, cache_hit_rate: float):
        """Log memory usage metrics."""
        self.logger.info(f"MEMORY_USAGE,{user_id},{memory_count},{cache_hit_rate:.3f}")
    
    def log_emotion_detection(self, user_id: str, emotion: str, confidence: float):
        """Log emotion detection metrics."""
        self.logger.info(f"EMOTION_DETECTION,{user_id},{emotion},{confidence:.3f}")
    
    def log_crisis_detection(self, user_id: str, risk_level: str, confidence: float):
        """Log crisis detection events."""
        self.logger.info(f"CRISIS_DETECTION,{user_id},{risk_level},{confidence:.3f}")


class SecurityLogger:
    """Logger for security events and audit trails."""
    
    def __init__(self, name: str = "security"):
        self.logger = logging.getLogger(name)
        self.security_file = Path("data/logs/security.log")
        self.security_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Create security-specific handler
        if not self.logger.handlers:
            handler = logging.FileHandler(self.security_file)
            formatter = logging.Formatter(
                '%(asctime)s - SECURITY - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def log_user_access(self, user_id: str, action: str, ip_address: str = None):
        """Log user access events."""
        ip_info = f" from {ip_address}" if ip_address else ""
        self.logger.info(f"USER_ACCESS,{user_id},{action}{ip_info}")
    
    def log_api_access(self, endpoint: str, user_id: str = None, ip_address: str = None):
        """Log API access events."""
        user_info = f",{user_id}" if user_id else ",anonymous"
        ip_info = f",{ip_address}" if ip_address else ",unknown"
        self.logger.info(f"API_ACCESS,{endpoint}{user_info}{ip_info}")
    
    def log_data_access(self, user_id: str, data_type: str, action: str):
        """Log data access events."""
        self.logger.info(f"DATA_ACCESS,{user_id},{data_type},{action}")
    
    def log_security_event(self, event_type: str, details: str, severity: str = "INFO"):
        """Log general security events."""
        log_method = getattr(self.logger, severity.lower(), self.logger.info)
        log_method(f"SECURITY_EVENT,{event_type},{details}")


# Global logger instances
performance_logger = PerformanceLogger()
security_logger = SecurityLogger()
