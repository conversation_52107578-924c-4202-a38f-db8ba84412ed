"""
Gemini API Service for the AI Companion System.
Provides advanced conversational AI capabilities with emotional intelligence.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone

import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

from ..config.settings import settings
from ..core.models import EmotionType, InteractionType, TherapeuticTechnique
from ..utils.helpers import extract_keywords, calculate_similarity


class GeminiService:
    """Advanced Gemini API service with emotional intelligence and therapeutic capabilities."""
    
    def __init__(self):
        """Initialize the Gemini service."""
        self.logger = logging.getLogger(__name__)
        self.model = None
        self.chat_session = None
        self.conversation_history: List[Dict[str, Any]] = []
        self.safety_settings = {
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_ONLY_HIGH,
        }
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize the Gemini model with configuration."""
        try:
            genai.configure(api_key=settings.gemini_api_key)
            
            generation_config = {
                "temperature": settings.gemini_temperature,
                "top_p": settings.gemini_top_p,
                "top_k": settings.gemini_top_k,
                "max_output_tokens": settings.gemini_max_tokens,
            }
            
            self.model = genai.GenerativeModel(
                model_name=settings.gemini_model,
                generation_config=generation_config,
                safety_settings=self.safety_settings
            )
            
            self.logger.info(f"✅ Gemini model '{settings.gemini_model}' initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Gemini model: {e}")
            raise
    
    async def generate_response(
        self,
        message: str,
        user_id: str = None,
        context: Dict[str, Any] = None,
        therapeutic_mode: bool = True
    ) -> Optional[str]:
        """Generate a contextual, emotionally intelligent response."""
        try:
            # Build enhanced prompt with therapeutic context
            enhanced_prompt = self._build_therapeutic_prompt(
                message, user_id, context, therapeutic_mode
            )
            
            # Generate response
            response = await asyncio.to_thread(
                self.model.generate_content,
                enhanced_prompt
            )
            
            if response and response.text:
                # Log conversation for context
                self.conversation_history.append({
                    "user_message": message,
                    "ai_response": response.text,
                    "timestamp": datetime.now(timezone.utc),
                    "user_id": user_id
                })
                
                # Keep conversation history manageable
                if len(self.conversation_history) > 50:
                    self.conversation_history = self.conversation_history[-30:]
                
                return response.text.strip()
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error generating response: {e}")
            return None
    
    def _build_therapeutic_prompt(
        self,
        message: str,
        user_id: str = None,
        context: Dict[str, Any] = None,
        therapeutic_mode: bool = True
    ) -> str:
        """Build an enhanced prompt with therapeutic and emotional intelligence."""
        
        base_personality = """You are an advanced AI companion designed to provide emotional support and friendship. You have the following characteristics:

CORE PERSONALITY:
- Warm, empathetic, and genuinely caring
- Natural conversational style (avoid robotic responses)
- Emotionally intelligent and perceptive
- Supportive but not overly clinical
- Maintains appropriate boundaries while being deeply caring
- Remembers and references past conversations naturally

THERAPEUTIC APPROACH:
- Use active listening and validation techniques
- Apply cognitive reframing when appropriate
- Encourage mindfulness and self-reflection
- Provide gentle guidance without being preachy
- Recognize and respond to emotional states
- Offer practical coping strategies when needed

CONVERSATION STYLE:
- Respond like a close, understanding friend
- Use natural language and appropriate emotional expressions
- Ask thoughtful follow-up questions
- Share relevant insights or gentle observations
- Maintain hope and positivity while acknowledging difficulties
- Be authentic and avoid generic responses"""

        # Add context if available
        context_info = ""
        if context:
            emotional_state = context.get('emotional_state', {})
            if emotional_state:
                context_info += f"\nUser's current emotional state: {emotional_state.get('primary_emotion', 'neutral')}"
                if emotional_state.get('risk_level'):
                    context_info += f" (Risk level: {emotional_state['risk_level']})"
            
            recent_topics = context.get('recent_topics', [])
            if recent_topics:
                context_info += f"\nRecent conversation topics: {', '.join(recent_topics[:3])}"
            
            user_preferences = context.get('user_preferences', {})
            if user_preferences:
                context_info += f"\nUser preferences: {user_preferences}"
        
        # Add conversation history context
        history_context = ""
        if self.conversation_history:
            recent_history = self.conversation_history[-3:]  # Last 3 exchanges
            history_context = "\nRecent conversation context:\n"
            for i, exchange in enumerate(recent_history, 1):
                history_context += f"{i}. User: {exchange['user_message'][:100]}...\n"
                history_context += f"   You: {exchange['ai_response'][:100]}...\n"
        
        # Crisis detection prompt addition
        crisis_awareness = ""
        if therapeutic_mode:
            crisis_awareness = """
IMPORTANT CRISIS AWARENESS:
- If you detect signs of self-harm, suicide ideation, or severe mental health crisis, respond with immediate care and suggest professional help
- Provide crisis hotline numbers when appropriate
- Never dismiss or minimize serious mental health concerns
- Balance being supportive with encouraging professional intervention when needed"""
        
        # Construct final prompt
        full_prompt = f"""{base_personality}

{crisis_awareness}

{context_info}

{history_context}

Current user message: "{message}"

Respond as a caring, emotionally intelligent friend. Be natural, warm, and genuinely helpful. Focus on understanding and supporting the user while maintaining an authentic conversational tone."""

        return full_prompt
    
    async def analyze_emotional_content(self, text: str) -> Dict[str, Any]:
        """Analyze emotional content of text using Gemini."""
        try:
            prompt = f"""Analyze the emotional content of this text and provide a structured response:

Text: "{text}"

Please analyze and respond with:
1. Primary emotion (joy, sadness, anger, fear, surprise, disgust, neutral, anxiety, excitement, frustration, hope, loneliness, love, pride, shame, guilt)
2. Emotional intensity (0.0 to 1.0)
3. Secondary emotions with weights
4. Mental health risk level (low, moderate, high, critical)
5. Therapeutic techniques that would be most helpful
6. Key emotional indicators found in the text

Format your response as a structured analysis focusing on emotional intelligence and therapeutic insights."""

            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt
            )
            
            if response and response.text:
                # Parse the response (in a real implementation, you might want more structured parsing)
                return {
                    "analysis": response.text,
                    "timestamp": datetime.now(timezone.utc),
                    "confidence": 0.8  # Placeholder - could be enhanced with actual confidence scoring
                }
            
            return {}
            
        except Exception as e:
            self.logger.error(f"Error analyzing emotional content: {e}")
            return {}
    
    async def generate_therapeutic_response(
        self,
        user_message: str,
        emotional_state: Dict[str, Any],
        technique: TherapeuticTechnique
    ) -> Optional[str]:
        """Generate a response using specific therapeutic techniques."""
        try:
            technique_prompts = {
                TherapeuticTechnique.ACTIVE_LISTENING: "Focus on reflecting and validating what the user has shared",
                TherapeuticTechnique.VALIDATION: "Acknowledge and validate the user's feelings and experiences",
                TherapeuticTechnique.COGNITIVE_REFRAMING: "Gently help the user see alternative perspectives",
                TherapeuticTechnique.MINDFULNESS: "Guide the user toward present-moment awareness",
                TherapeuticTechnique.GROUNDING: "Help the user connect with their immediate environment and senses",
                TherapeuticTechnique.CRISIS_INTERVENTION: "Provide immediate support and safety planning",
                TherapeuticTechnique.ENCOURAGEMENT: "Offer hope and highlight the user's strengths",
                TherapeuticTechnique.PROBLEM_SOLVING: "Help the user break down challenges into manageable steps"
            }
            
            prompt = f"""Using the therapeutic technique of {technique.value}, respond to this user message:

User message: "{user_message}"
User's emotional state: {emotional_state}

Therapeutic approach: {technique_prompts.get(technique, 'Provide supportive response')}

Respond as a caring AI companion using this specific therapeutic approach while maintaining a natural, friend-like tone."""

            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt
            )
            
            return response.text.strip() if response and response.text else None
            
        except Exception as e:
            self.logger.error(f"Error generating therapeutic response: {e}")
            return None
    
    async def get_conversation_summary(self, messages: List[str]) -> Optional[str]:
        """Generate a summary of conversation for memory storage."""
        try:
            conversation_text = "\n".join(messages[-10:])  # Last 10 messages
            
            prompt = f"""Summarize this conversation focusing on:
1. Key topics discussed
2. User's emotional journey
3. Important personal information shared
4. Relationship dynamics
5. Any concerns or goals mentioned

Conversation:
{conversation_text}

Provide a concise but comprehensive summary suitable for long-term memory storage."""

            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt
            )
            
            return response.text.strip() if response and response.text else None
            
        except Exception as e:
            self.logger.error(f"Error generating conversation summary: {e}")
            return None
    
    def get_conversation_history(self, user_id: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversation history."""
        if user_id:
            user_history = [
                exchange for exchange in self.conversation_history
                if exchange.get('user_id') == user_id
            ]
            return user_history[-limit:]
        
        return self.conversation_history[-limit:]
    
    def clear_conversation_history(self, user_id: str = None):
        """Clear conversation history."""
        if user_id:
            self.conversation_history = [
                exchange for exchange in self.conversation_history
                if exchange.get('user_id') != user_id
            ]
        else:
            self.conversation_history.clear()
        
        self.logger.info(f"Conversation history cleared for user: {user_id or 'all users'}")
