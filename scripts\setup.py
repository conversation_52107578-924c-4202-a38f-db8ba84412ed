#!/usr/bin/env python3
"""
Setup script for the AI Companion System.
Helps with initial setup and configuration.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def print_banner():
    """Print setup banner."""
    print("=" * 60)
    print("🧠 AI Companion System - Setup Script")
    print("=" * 60)
    print()


def check_python_version():
    """Check if Python version is compatible."""
    print("📋 Checking Python version...")
    
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True


def check_git():
    """Check if git is available."""
    print("📋 Checking Git...")
    
    try:
        subprocess.run(["git", "--version"], capture_output=True, check=True)
        print("✅ Git is available")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  Git not found (optional for development)")
        return False


def create_virtual_environment():
    """Create Python virtual environment."""
    print("🐍 Creating virtual environment...")
    
    venv_path = Path("venv")
    
    if venv_path.exists():
        print("✅ Virtual environment already exists")
        return True
    
    try:
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("✅ Virtual environment created")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create virtual environment: {e}")
        return False


def get_pip_command():
    """Get the appropriate pip command for the platform."""
    if os.name == 'nt':  # Windows
        return str(Path("venv") / "Scripts" / "pip")
    else:  # Unix-like
        return str(Path("venv") / "bin" / "pip")


def install_dependencies():
    """Install Python dependencies."""
    print("📦 Installing dependencies...")
    
    pip_cmd = get_pip_command()
    
    try:
        # Upgrade pip first
        subprocess.run([pip_cmd, "install", "--upgrade", "pip"], check=True)
        
        # Install requirements
        subprocess.run([pip_cmd, "install", "-r", "requirements.txt"], check=True)
        
        print("✅ Dependencies installed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def setup_environment_file():
    """Setup environment configuration file."""
    print("⚙️  Setting up environment configuration...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if not env_example.exists():
        print("❌ .env.example file not found")
        return False
    
    try:
        shutil.copy(env_example, env_file)
        print("✅ .env file created from template")
        print("⚠️  Please edit .env file with your API keys and configuration")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False


def create_data_directories():
    """Create necessary data directories."""
    print("📁 Creating data directories...")
    
    directories = [
        "data",
        "data/logs",
        "data/db",
        "data/cache"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Data directories created")
    return True


def check_api_key():
    """Check if API key is configured."""
    print("🔑 Checking API key configuration...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  .env file not found")
        return False
    
    try:
        with open(env_file, 'r') as f:
            content = f.read()
        
        if "GEMINI_API_KEY=your_gemini_api_key_here" in content:
            print("⚠️  Please set your GEMINI_API_KEY in .env file")
            print("   Get your API key from: https://makersuite.google.com/app/apikey")
            return False
        elif "GEMINI_API_KEY=" in content:
            print("✅ GEMINI_API_KEY is configured")
            return True
        else:
            print("⚠️  GEMINI_API_KEY not found in .env file")
            return False
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False


def test_installation():
    """Test the installation."""
    print("🧪 Testing installation...")
    
    python_cmd = get_python_command()
    
    try:
        # Test import
        result = subprocess.run([
            python_cmd, "-c", 
            "import sys; sys.path.insert(0, 'src'); from ai_companion.core.models import EmotionType; print('✅ Import test passed')"
        ], capture_output=True, text=True, check=True)
        
        print(result.stdout.strip())
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Import test failed: {e}")
        print(f"   Error output: {e.stderr}")
        return False


def get_python_command():
    """Get the appropriate Python command for the platform."""
    if os.name == 'nt':  # Windows
        return str(Path("venv") / "Scripts" / "python")
    else:  # Unix-like
        return str(Path("venv") / "bin" / "python")


def print_next_steps():
    """Print next steps for the user."""
    print()
    print("🎉 Setup completed successfully!")
    print()
    print("Next steps:")
    print("1. Edit .env file with your API keys:")
    print("   - Get Gemini API key: https://makersuite.google.com/app/apikey")
    print("   - Set GEMINI_API_KEY in .env file")
    print()
    print("2. Activate virtual environment:")
    if os.name == 'nt':  # Windows
        print("   venv\\Scripts\\activate")
    else:  # Unix-like
        print("   source venv/bin/activate")
    print()
    print("3. Run the AI Companion System:")
    print("   python -m src.ai_companion.main")
    print()
    print("4. Access the interface:")
    print("   - Web Interface: http://localhost:7860")
    print("   - API Documentation: http://localhost:8000/docs")
    print()


def main():
    """Main setup function."""
    print_banner()
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    check_git()
    
    # Setup steps
    steps = [
        create_virtual_environment,
        install_dependencies,
        setup_environment_file,
        create_data_directories,
        test_installation
    ]
    
    for step in steps:
        if not step():
            print(f"\n❌ Setup failed at step: {step.__name__}")
            sys.exit(1)
        print()
    
    # Check API key (non-blocking)
    check_api_key()
    
    # Print next steps
    print_next_steps()


if __name__ == "__main__":
    main()
