"""
Emotion Detection Patterns and Pattern Matching.
"""

import re
import logging
from typing import Dict, List, Optional
from collections import defaultdict

from ..models import EmotionType, MentalHealthRisk


class EmotionPatternMatcher:
    """Handles pattern-based emotion detection."""
    
    def __init__(self):
        """Initialize emotion pattern matcher."""
        self.logger = logging.getLogger(__name__)
        self.emotion_patterns = self._initialize_emotion_patterns()
        self.crisis_indicators = self._initialize_crisis_indicators()
    
    def _initialize_emotion_patterns(self) -> Dict[EmotionType, List[str]]:
        """Initialize emotion detection patterns."""
        return {
            EmotionType.JOY: [
                r'\b(happy|joy|joyful|excited|thrilled|delighted|cheerful|glad|pleased)\b',
                r'\b(amazing|wonderful|fantastic|great|awesome|brilliant|excellent)\b',
                r'\b(love|loving|adore|enjoy|fun|celebration|celebrate)\b',
                r':\)|😊|😄|😃|🎉|❤️|💕'
            ],
            EmotionType.SADNESS: [
                r'\b(sad|sadness|depressed|down|blue|melancholy|gloomy|miserable)\b',
                r'\b(crying|tears|weeping|sobbing|heartbroken|devastated)\b',
                r'\b(lonely|alone|isolated|abandoned|empty|hopeless)\b',
                r':\(|😢|😭|💔|😞|😔'
            ],
            EmotionType.ANGER: [
                r'\b(angry|mad|furious|rage|irritated|annoyed|frustrated|pissed)\b',
                r'\b(hate|hatred|disgusted|outraged|livid|seething)\b',
                r'\b(damn|hell|stupid|idiot|fuck|shit)\b',
                r'😠|😡|🤬|💢'
            ],
            EmotionType.FEAR: [
                r'\b(afraid|scared|frightened|terrified|anxious|worried|nervous)\b',
                r'\b(panic|panicking|dread|horror|phobia|paranoid)\b',
                r'\b(threat|danger|unsafe|vulnerable|helpless)\b',
                r'😨|😰|😱|😧'
            ],
            EmotionType.SURPRISE: [
                r'\b(surprised|shocked|amazed|astonished|stunned|bewildered)\b',
                r'\b(unexpected|sudden|wow|omg|unbelievable|incredible)\b',
                r'😲|😮|🤯|😯'
            ],
            EmotionType.DISGUST: [
                r'\b(disgusted|revolted|repulsed|sickened|nauseated)\b',
                r'\b(gross|yuck|eww|horrible|awful|terrible)\b',
                r'🤢|🤮|😷'
            ],
            EmotionType.ANXIETY: [
                r'\b(anxious|anxiety|worried|stress|stressed|overwhelmed)\b',
                r'\b(nervous|tense|restless|uneasy|apprehensive)\b',
                r'\b(panic|racing thoughts|can\'t relax|on edge)\b'
            ],
            EmotionType.EXCITEMENT: [
                r'\b(excited|thrilled|pumped|energetic|enthusiastic)\b',
                r'\b(can\'t wait|looking forward|eager|anticipating)\b',
                r'🎉|🎊|✨|🔥'
            ],
            EmotionType.FRUSTRATION: [
                r'\b(frustrated|frustrating|annoying|irritating)\b',
                r'\b(stuck|blocked|can\'t figure out|giving up)\b',
                r'\b(why won\'t|this is impossible|so difficult)\b'
            ],
            EmotionType.HOPE: [
                r'\b(hope|hopeful|optimistic|positive|confident)\b',
                r'\b(better|improve|getting better|looking up)\b',
                r'\b(believe|faith|trust|possibility|maybe)\b'
            ],
            EmotionType.LONELINESS: [
                r'\b(lonely|alone|isolated|nobody|no one)\b',
                r'\b(by myself|on my own|solitary|friendless)\b',
                r'\b(disconnected|cut off|withdrawn|apart)\b'
            ],
            EmotionType.LOVE: [
                r'\b(love|loving|adore|cherish|treasure|devoted)\b',
                r'\b(affection|care|caring|tender|warm|close)\b',
                r'❤️|💕|💖|💗|💝|😍|🥰'
            ],
            EmotionType.PRIDE: [
                r'\b(proud|pride|accomplished|achieved|succeeded)\b',
                r'\b(did it|made it|completed|finished|won)\b',
                r'\b(satisfied|fulfilled|pleased with myself)\b'
            ],
            EmotionType.SHAME: [
                r'\b(ashamed|shame|embarrassed|humiliated|mortified)\b',
                r'\b(stupid|foolish|regret|sorry|my fault)\b',
                r'\b(shouldn\'t have|wish I hadn\'t|feel bad about)\b'
            ],
            EmotionType.GUILT: [
                r'\b(guilty|guilt|blame|responsible|my fault)\b',
                r'\b(should have|shouldn\'t have|regret|sorry)\b',
                r'\b(let down|disappointed|hurt someone)\b'
            ]
        }
    
    def _initialize_crisis_indicators(self) -> Dict[str, List[str]]:
        """Initialize crisis detection patterns."""
        return {
            "high_risk": [
                r'\b(kill myself|end it all|suicide|want to die|better off dead)\b',
                r'\b(cut myself|hurt myself|self harm|harm myself)\b',
                r'\b(can\'t go on|no point|worthless|hopeless)\b'
            ],
            "moderate_risk": [
                r'\b(depressed|depression|can\'t cope|overwhelmed)\b',
                r'\b(panic attack|anxiety attack|can\'t breathe)\b',
                r'\b(drinking too much|using drugs|addiction)\b'
            ],
            "emotional_distress": [
                r'\b(crying|tears|breakdown|falling apart)\b',
                r'\b(exhausted|drained|empty|numb)\b',
                r'\b(lost|confused|don\'t know what to do)\b'
            ]
        }
    
    def detect_emotions_by_pattern(self, text: str) -> Dict[EmotionType, float]:
        """Detect emotions using regex patterns."""
        text_lower = text.lower()
        emotion_scores = defaultdict(float)
        
        for emotion, patterns in self.emotion_patterns.items():
            total_matches = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, text_lower, re.IGNORECASE))
                total_matches += matches
            
            if total_matches > 0:
                # Normalize score based on text length and pattern matches
                text_length = len(text.split())
                normalized_score = min(total_matches / max(text_length / 10, 1), 1.0)
                emotion_scores[emotion] = normalized_score
        
        return dict(emotion_scores)
    
    def assess_crisis_risk(self, text: str) -> MentalHealthRisk:
        """Assess mental health crisis risk based on patterns."""
        text_lower = text.lower()
        
        # Check for high-risk indicators
        for pattern in self.crisis_indicators["high_risk"]:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return MentalHealthRisk.CRITICAL
        
        # Check for moderate-risk indicators
        moderate_matches = 0
        for pattern in self.crisis_indicators["moderate_risk"]:
            if re.search(pattern, text_lower, re.IGNORECASE):
                moderate_matches += 1
        
        if moderate_matches >= 2:
            return MentalHealthRisk.HIGH
        elif moderate_matches >= 1:
            return MentalHealthRisk.MODERATE
        
        # Check for emotional distress
        distress_matches = 0
        for pattern in self.crisis_indicators["emotional_distress"]:
            if re.search(pattern, text_lower, re.IGNORECASE):
                distress_matches += 1
        
        if distress_matches >= 2:
            return MentalHealthRisk.MODERATE
        elif distress_matches >= 1:
            return MentalHealthRisk.LOW
        
        return MentalHealthRisk.LOW
    
    def get_emotion_keywords(self, emotion: EmotionType) -> List[str]:
        """Get keywords for a specific emotion."""
        patterns = self.emotion_patterns.get(emotion, [])
        keywords = []
        
        for pattern in patterns:
            # Extract simple keywords from regex patterns
            # This is a simplified extraction - in practice, you might want more sophisticated parsing
            clean_pattern = pattern.replace(r'\b', '').replace('(', '').replace(')', '').replace('|', ' ')
            words = [word.strip() for word in clean_pattern.split() if word.strip()]
            keywords.extend(words)
        
        return list(set(keywords))  # Remove duplicates
    
    def add_emotion_pattern(self, emotion: EmotionType, pattern: str):
        """Add a new pattern for emotion detection."""
        if emotion not in self.emotion_patterns:
            self.emotion_patterns[emotion] = []
        
        self.emotion_patterns[emotion].append(pattern)
        self.logger.info(f"Added pattern for {emotion.value}: {pattern}")
    
    def remove_emotion_pattern(self, emotion: EmotionType, pattern: str):
        """Remove a pattern from emotion detection."""
        if emotion in self.emotion_patterns and pattern in self.emotion_patterns[emotion]:
            self.emotion_patterns[emotion].remove(pattern)
            self.logger.info(f"Removed pattern for {emotion.value}: {pattern}")
    
    def get_all_patterns(self) -> Dict[EmotionType, List[str]]:
        """Get all emotion patterns."""
        return self.emotion_patterns.copy()
    
    def update_crisis_indicators(self, risk_level: str, patterns: List[str]):
        """Update crisis indicator patterns."""
        if risk_level in self.crisis_indicators:
            self.crisis_indicators[risk_level] = patterns
            self.logger.info(f"Updated crisis indicators for {risk_level}")
    
    def get_crisis_indicators(self) -> Dict[str, List[str]]:
        """Get all crisis indicator patterns."""
        return self.crisis_indicators.copy()
